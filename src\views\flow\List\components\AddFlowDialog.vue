<script setup lang="ts">
// ElMessage,
import { type FormInstance } from 'element-plus'
import type { ISaveWorkflowInfoParams } from '../../api/index.ts'
import { saveWorkflowInfo } from '../../api/index.ts'

const emits = defineEmits(['updateList'])

const title = '新增流程'
const visible = ref(false)
const ruleFormRef = ref<FormInstance>()
const formData = ref({
  wfName: '',
  wfDescription: '',
})

const rules = {
  wfName: [
    {
      required: true,
      message: '请输入流程名称',
      trigger: 'blur',
    },
  ],
  wfDescription: [
    {
      required: false,
      message: '请输入流程描述',
      trigger: 'blur',
    },
  ],
}

const openDialog = () => {
  visible.value = true
}

const closeDialog = () => {
  visible.value = false
  ruleFormRef.value?.resetFields()
}

const addWorkflow = async () => {
  const params: ISaveWorkflowInfoParams = formData.value
  try {
    await ruleFormRef.value?.validate()
    const res = await saveWorkflowInfo(params)
    if (res.code == '000000') {
      emits('updateList')
      ElMessage.success('新增工作流成功')
      closeDialog()
    }
  } catch (error) {
    console.error('验证失败', error)
  }
}

defineExpose({
  openDialog,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" width="600px" @close="closeDialog">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" class="right-form" label-position="top">
      <el-form-item label="流程名称" prop="wfName">
        <el-input v-model="formData.wfName" placeholder="请输入流程名称" />
      </el-form-item>
      <el-form-item label="流程描述" prop="wfDescription">
        <el-input v-model="formData.wfDescription" type="textarea" placeholder="请输入流程描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="addWorkflow" :disabled="!formData.wfName">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
