import { ref, nextTick, onUnmounted } from 'vue'

export function usePercentage(options = {}) {
  let timer = null
  const { start = 0, speed = 20, interval = 2000 } = options
  const percentage = ref(start)

  onUnmounted(() => {
    clearInterval(timer)
  })

  function setDone(options = {}) {
    const { total = 100, duration = 0 } = options
    clearInterval(timer)
    percentage.value = total
    return new Promise((resolve) => {
      nextTick(() => {
        let setTimer = setTimeout(() => {
          clearTimeout(setTimer)
          resolve(true)
        }, duration)
      })
    })
  }

  function goProgress() {
    timer = setInterval(() => {
      const randomNum = Math.floor(Math.random() * speed) + 1
      if (randomNum < 50) {
        percentage.value += randomNum
      }
      if (percentage.value > 99) {
        percentage.value = 99
        clearInterval(timer)
      }
    }, interval)
  }

  function setStart() {
    goProgress()
  }
  function setReset(val) {
    percentage.value = val || start
  }

  function setStop() {
    clearInterval(timer)
  }

  return {
    percentage,
    setStart,
    setStop,
    setReset,
    setDone,
  }
}
