import { createApp, type App, type ComponentPublicInstance } from 'vue'
import Annex from './BoxTip.vue'

interface NodeData {
  suType: number
  id: string | number
  [key: string]: any
}

interface ShowDetailOptions {
  node: NodeData
}

interface ComponentInstance extends ComponentPublicInstance {
  $el: HTMLElement
  updateNode?: (node: NodeData) => void
  show?: () => void
}

let instance: ComponentInstance | null = null
let pEl: HTMLElement | null = null
let app: App | null = null

const initInstance = (parentEl: HTMLElement, suType: number = 1): void => {
  if (!instance) {
    switch (suType) {
      case 1:
        // Vue 3 的创建方式
        const mountEl = document.createElement('div')
        app = createApp(Annex)
        instance = app.mount(mountEl) as ComponentInstance
        // 在 Vue 3 中，mount 返回的是组件实例，mountEl 是实际的 DOM 元素
        instance.$el = mountEl
        break
      case 2:
        break

      default:
        break
    }
  }
  pEl = parentEl
  if (instance?.$el) {
    parentEl.appendChild(instance.$el)
  }
}

const clearDetail = function (): void {
  if (instance && app) {
    app.unmount()
    if (pEl && instance.$el && pEl.contains(instance.$el)) {
      pEl.removeChild(instance.$el)
    }
    pEl = null
    instance = null
    app = null
  }
}

const showDetail = function (options: ShowDetailOptions, parentEl: HTMLElement): ComponentInstance | null {
  if (!instance) {
    initInstance(parentEl, options.node.suType)
  }

  // 确保 instance 存在后再调用方法
  if (instance) {
    // 使用 updateNode 方法更新节点数据
    if (instance.updateNode && typeof instance.updateNode === 'function') {
      instance.updateNode(options.node)
    }
    if (instance.show && typeof instance.show === 'function') {
      instance.show()
    }
  }

  return instance
}

export { showDetail, clearDetail }
