import request from '@/services'
import type { IResponse } from '@/services'

// 登录
// 参数
export interface ILoginParams {
  userName: string
  password: string
}
// 接口
export function requesetLogin<T>(data: ILoginParams): Promise<IResponse<T>> {
  return request({
    url: `/login/loginByPwd`,
    method: 'post',
    data,
  })
}

// 退出登录
export function requesetLogout<T>(): Promise<IResponse<T>> {
  return request({
    url: `/account/logout`,
    method: 'get',
  })
}

// 获取用户信息
export function queryUserData<T>(): Promise<IResponse<T>> {
  return request({
    url: `/account/getCurrUser`,
    method: 'get',
  })
}

export interface IEditPasswordParams {
  oldPassword: string
  newPassword: string
}
// 修改密码
export function editPassword<T>(data: IEditPasswordParams): Promise<IResponse<T>> {
  return request({
    url: `/account/updatePwd`,
    method: 'post',
    data,
  })
}
