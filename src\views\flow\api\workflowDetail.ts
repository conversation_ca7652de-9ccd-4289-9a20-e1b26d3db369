import request from '@/services'
import type { IResponse } from '@/services'

// 获取工作流信息参数
// 响应参数
export interface IQueryWorkflowData {
  id: string | number
  wfName: string
  wfDescription: string
  appId: string
  wfGraph?: string
  createBy: string
  createByName: string
  createTime: string
  updateTime?: string
  updateBy?: string
  updateByName?: string
}
// 获取工作流信息接口
export function queryWorkflowData<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/workflow/getWorkflowInfo/${id}`,
    method: 'get',
  })
}

// 查询当前可用对话模型列表参数
export interface IQueryModelList {
  id: string
  modelName: string // 模型名称
  baseUrl: string // 模型请求地址
  apiKeyValue: string // 模型密钥
  modelType: number // 模型类型
  modelTypeName: string // 模型类型名称
  remark: string // 备注
  enabled: boolean // 是否启用
  createBy: string
  createByName: string
  createTime: string
  updateBy: string
  updateByName: string
  updateTime: string
  corpId?: string // 租户
}
// 查询当前可用对话模型列表接口
export function queryModelList<T>(): Promise<IResponse<T>> {
  return request({
    url: `/model/llm/list`,
    method: 'get',
  })
}

// 保存工作流图信息
export interface ISaveWorkflowGraphParams {
  id: string
  wfGraph?: string
}
export function saveWorkflowGraph<T>(data: ISaveWorkflowGraphParams): Promise<IResponse<T>> {
  return request({
    url: `/workflow/saveGraphInfo`,
    method: 'post',
    data,
  })
}
