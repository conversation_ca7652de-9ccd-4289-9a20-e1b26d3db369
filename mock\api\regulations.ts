import Mock from 'mockjs'
export const regulations = [
  {
    url: /^\/api\/bff-iterms-manage\/mock\/lawInfo\/lawDetail\/(.+)$/,
    method: 'get',
    response: (req) => {
      return {
        code: '000000',
        message: 'success',
        success: true,
        data: {
          createBy: '@string(5,10)',
          updateBy: '@string(5,10)',
          createTime: '@datetime',
          updateTime: '@datetime',
          corpId: '@id',
          createByName: '@cname',
          updateByName: '@cname',
          corpName: '@cname',
          id: '@id',
          topicContent: '@ctitle(10,30)',
          chapterContent: '@ctitle(10,30)',
          jointContent: '@ctitle(10,30)',
          provisionsContent: '@ctitle(100,170)',
          labels: [
            {
              createBy: '@string(5,10)',
              updateBy: '@string(5,10)',
              createTime: '@datetime',
              updateTime: '@datetime',
              corpId: '@id',
              createByName: '@cname',
              updateByName: '@cname',
              corpName: '@cname',
              id: '@id',
              bizType: '@id',
              bizTypeName: '@ctitle(5,10)',
              labelName: '@ctitle(5,10)',
              enableStatus: true,
            },
          ],
        },
      }
    },
  },
  {
    url: '/api/bff-iterms-manage/mock/lawInfo/lawDetailPage',
    method: 'post',
    response: (req) => {
      const { pageNum = 1, pageSize = 10 } = req.query
      const total = 56
      const totalPages = Math.ceil(total / pageSize)
      return {
        code: 200,
        message: 'success',
        success: true,
        data: {
          pageNum,
          pageSize,
          total,
          totalPages,
          list: Mock.mock({
            [`items|${pageSize}`]: [
              {
                createBy: '@string(5,10)',
                updateBy: '@string(5,10)',
                createTime: '@datetime',
                updateTime: '@datetime',
                corpId: '@id',
                createByName: '@cname',
                updateByName: '@cname',
                corpName: '@cname',
                id: '@id',
                lawName: '@ctitle(10,30)',
                topicContent: '@ctitle(100,150)',
                chapterContent: '@ctitle(10,15)',
                jointContent: '@ctitle(10,15)',
                provisionsContent: '@ctitle(100,150)',
                labels: [
                  {
                    id: '@id',
                    labelName: '@ctitle(5,10)',
                  },
                ],
              },
            ],
          }).items,
        },
      }
    },
  },
  {
    url: /^\/api\/bff-iterms-manage\/mock\/lawInfo\/law\/(.+)$/,
    method: 'get',
    response: (req) => {
      return {
        code: '000000',
        message: 'success',
        success: true,
        data: {
          createBy: '@string(5,10)',
          updateBy: '@string(5,10)',
          createTime: '@datetime',
          updateTime: '@datetime',
          corpId: '@id',
          createByName: '@cname',
          updateByName: '@cname',
          corpName: '@cname',
          id: '@id',
          lawName: '@ctitle(10,30)',
          lawPreface: '@cname',
          currentValid: true,
          lawDegree: '@integer(1,5)',
          originType: 531,
          publishDate: '@datetime',
          lawArticleNo: '@string',
          executeDate: '@datetime',
          labels: [
            {
              createBy: '@string(5,10)',
              updateBy: '@string(5,10)',
              createTime: '@datetime',
              updateTime: '@datetime',
              corpId: '@id',
              createByName: '@cname',
              updateByName: '@cname',
              corpName: '@cname',
              id: '@id',
              bizType: '@id',
              bizTypeName: '@ctitle(5,10)',
              labelName: '@ctitle(5,10)',
              enableStatus: true,
            },
          ],
        },
      }
    },
  },
  {
    url: '/api/bff-iterms-manage/mock/lawInfo/lawPage',
    method: 'post',
    response: (req) => {
      const { pageNum = 1, pageSize = 10 } = req.query
      const total = 56
      const totalPages = Math.ceil(total / pageSize)
      return {
        code: 200,
        message: 'success',
        success: true,
        data: {
          pageNum,
          pageSize,
          total,
          totalPages,
          list: Mock.mock({
            [`items|${pageSize}`]: [
              {
                createBy: '@string(5,10)',
                updateBy: '@string(5,10)',
                createTime: '@datetime',
                updateTime: '@datetime',
                corpId: '@id',
                createByName: '@cname',
                updateByName: '@cname',
                corpName: '@cname',
                id: '@id',
                lawName: '@ctitle(10,30)',
                lawPreface: '@cname',
                currentValid: true,
                lawDegree: '@integer(1,5)',
                originType: 531,
                publishDate: '@datetime',
                lawArticleNo: '@string',
                executeDate: '@datetime',
              },
            ],
          }).items,
        },
      }
    },
  },
  {
    url: '/api/bff-iterms-manage/mock/label/bizLabels',
    method: 'get',
    response: (req) => {
      const { count = 10 } = req.body
      return {
        code: 200,
        message: 'success',
        success: true,
        data: Mock.mock({
          [`items|${count}`]: [
            {
              createBy: '@cname',
              updateBy: '@cname',
              createTime: '@datetime',
              updateTime: '@datetime',
              corpId: '@id',
              createByName: '@cname',
              updateByName: '@cname',
              corpName: '@cname',
              id: '@id',
              bizType: '@id',
              bizTypeName: '@ctitle(5,6)',
              enableStatus: true,
              labelName: '@ctitle(8,8)',
            },
          ],
        }).items,
      }
    },
  },
  {
    url: /^\/api\/bff-iterms-manage\/mock\/lawCase\/detail\/(\d+)$/,
    method: 'post',
    response: (req) => {
      const matchResult = req.url.match(/^\/api\/bff-iterms-manage\/lawCase\/detail\/(\d+)$/)
      const caseId = matchResult ? matchResult[1] : ''
      return {
        code: 200,
        message: 'success',
        success: true,
        data: {
          createBy: '@cname',
          updateBy: '@cname',
          createTime: '@datetime',
          updateTime: '@datetime',
          corpId: '@id',
          createByName: '@cname',
          updateByName: '@cname',
          corpName: '@cname',
          id: '@id',
          caseNumber: '@string',
          caseName: '@ctitle(10, 30)',
          caseSummary: '@ctitle(10, 430)',
          casePoint: '@ctitle(10, 30)',
          caseReason: '@ctitle(10, 30)',
          caseEvaluate: '@ctitle(10, 30)',
          caseLocation: '@ctitle(10, 30)',
          lawSuit: '@ctitle(10, 30)',
          courtName: '@cname',
          courtViewPoint: '@ctitle(10, 30)',
          courtEvidence: '@ctitle(10, 30)',
          judgeResults: '@ctitle(10, 30)',
          judgePoint: '@ctitle(10, 30)',
          judgeTime: '@datetime',
          judgeTimeStr: '@datetime',
        },
      }
    },
  },
  {
    url: '/api/bff-iterms-manage/mock/lawCase/page',
    method: 'post',
    response: (req) => {
      const { pageNum = 1, pageSize = 10 } = req.query
      const total = 56
      const totalPages = Math.ceil(total / pageSize)
      return {
        code: 200,
        message: 'success',
        success: true,
        data: {
          pageNum,
          pageSize,
          total,
          totalPages,
          list: Mock.mock({
            [`items|${pageSize}`]: [
              {
                createBy: '@cname',
                updateBy: '@cname',
                createTime: '@datetime',
                updateTime: '@datetime',
                corpId: '@id',
                createByName: '@cname',
                updateByName: '@cname',
                corpName: '@cname',
                id: '@id',
                caseNumber: '@string',
                caseName: '@ctitle(10, 30)',
                caseSummary: '@ctitle(10, 130)',
                casePoint: '@ctitle(10, 30)',
                caseReason: '@ctitle(10, 30)',
                caseEvaluate: '@ctitle(10, 30)',
                caseLocation: '@ctitle(10, 30)',
                lawSuit: '@ctitle(10, 30)',
                courtName: '@cname',
                courtViewPoint: '@ctitle(10, 30)',
                courtEvidence: '@ctitle(10, 30)',
                judgeResults: '@ctitle(10, 30)',
                judgePoint: '@ctitle(10, 30)',
                judgeTime: '@datetime',
                judgeTimeStr: '@datetime',
              },
            ],
          }).items,
        },
      }
    },
  },
]
