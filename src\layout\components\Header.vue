<script setup lang="ts">
interface Props {
  showBackArrow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBackArrow: false,
})

const route = useRoute()
</script>

<template>
  <div class="header-wrap">
    <div class="header-left">
      <i v-if="props.showBackArrow" class="icon-nexus-arrowRight back"></i>
      <span class="header-name">{{ route.meta?.title || '' }}</span>
    </div>
    <slot name="option"></slot>
    <!-- 插槽示例代码（多个按钮需要放到一个容器中）
      <span>
        <el-button type="primary">操作按钮</el-button>
        <el-button>操作按钮</el-button>
      </span> 
    -->
    <slot></slot>
  </div>
</template>

<style lang="scss" scoped>
.header-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid #eaebf0;
  .header-left {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .back {
      position: relative;
      padding: 5px;
      margin-right: 20px;
      cursor: pointer;
      border: 1px solid transparent;
      &::before {
        display: inline-block;
        font-size: 15px;
        font-weight: bold;
        color: #221d39;
        transform: rotate(180deg);
      }
      &::after {
        position: absolute;
        top: 6px;
        right: -6px;
        width: 1px;
        height: 16px;
        content: '';
        background-color: #eaebf0;
      }
      &:hover {
        background-color: #f2f3f6;
        border-radius: 4px;
      }
    }
    .header-name {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }
}
</style>
