import { downloadStream } from '@/services/app'

// 定义参数类型
interface DownloadFileParams {
  fileCode?: string
  fileName?: string
  fileApi?: [(...args: any[]) => Promise<any>, any]
}

// 导出文件函数
function exportFile(data: Blob, fileName: string) {
  const blob = new Blob([data])
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

async function downloadFile(obj: DownloadFileParams) {
  let { fileCode, fileName } = obj
  const { fileApi } = obj
  let resData: any

  if (!fileApi) {
    resData = await downloadStream({ fileCode, fileName })
  } else {
    const api = fileApi[0]
    const params = fileApi[1]
    fileCode = params.fileCode
    console.log('api', api)
    console.log('params', params)
    resData = await api(params)
  }

  if (!fileName) {
    const contentDisposition = resData.headers['content-disposition']?.split(';')[1]?.split('=')[1] || ''
    fileName = decodeURI(contentDisposition)
  } else if (fileCode) {
    let suffix = fileCode.substring(fileCode.lastIndexOf('.'))
    if (fileName.lastIndexOf(suffix) !== -1) {
      fileName = fileName.substring(0, fileName.lastIndexOf(suffix))
    }
    if (suffix === fileCode) {
      // fileCode 加密了没有文件后缀
      const contentDisposition = resData.headers['content-disposition']?.split(';')[1]?.split('=')[1] || ''
      const _fileName = decodeURI(contentDisposition)
      suffix = _fileName.substring(_fileName.lastIndexOf('.'))
      if (fileName.lastIndexOf(suffix) !== -1) {
        fileName = fileName.substring(0, fileName.lastIndexOf(suffix))
      }
    }
    fileName = fileName + suffix
  }

  exportFile(resData.data, fileName)
}

export default downloadFile
