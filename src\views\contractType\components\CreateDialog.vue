<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    :title="dialogTitle"
    append-to-body
    :close-on-click-modal="false"
    width="500px"
    show-close
    @close="close"
  >
    <el-form ref="refForm" label-width="110px" :model="ruleForm" :rules="rules" label-position="top">
      <el-form-item label="合同类型名称" prop="contractTypeName">
        <el-input v-model="ruleForm.contractTypeName" show-word-limit maxlength="30" clearable placeholder="请输入合同类型名称" />
      </el-form-item>
      <div class="footer">
        <el-button @click="close" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleCommit" :loading="loading">确认</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  addContractType,
  editContractType,
  type ContractTypeItem
} from '@/services/contractApi'

// 定义 emits
const emit = defineEmits<{
  refresh: []
}>()

const rules = reactive({
  contractTypeName: [
    { required: true, message: '请输入合同类型名称', trigger: 'blur' },
    { min: 2, max: 30, message: '长度限制在2-30个字', trigger: 'blur' },
  ],
})

const visible = ref(false)
const mode = ref<'add' | 'edit' | 'addChild'>('add') // 模式：'add' 新增，'edit' 编辑，'addChild' 新增子类型
const editId = ref('') // 编辑时的ID
const parentData = ref<ContractTypeItem | null>(null) // 父级数据（新增子类型时使用）
const loading = ref(false) // 提交loading状态

// 动态标题
const dialogTitle = computed(() => {
  if (mode.value === 'edit') {
    return '编辑'
  } else if (mode.value === 'addChild') {
    return '新增子合同类型'
  } else {
    return '新增合同类型'
  }
})

const iniData = {
  contractTypeName: '',
}
const ruleForm = ref(iniData)

async function open(editData?: any, parent?: ContractTypeItem) {
  if (editData) {
    // 编辑模式
    mode.value = 'edit'
    editId.value = editData.id
    ruleForm.value.contractTypeName = editData.contractTypeName
  } else if (parent) {
    // 新增子类型模式
    mode.value = 'addChild'
    parentData.value = parent
    editId.value = ''
    ruleForm.value = { ...iniData }
  } else {
    // 新增模式
    mode.value = 'add'
    editId.value = ''
    parentData.value = null
    ruleForm.value = { ...iniData }
  }
  visible.value = true
}

function close() {
  refForm.value?.resetFields()
  visible.value = false
  // 重置模式状态
  mode.value = 'add'
  editId.value = ''
  parentData.value = null
}

const refForm = ref<any>(null)

function handleCommit() {
  refForm.value?.validate(async (valid: any, fields: any) => {
    if (valid) {
      loading.value = true
      try {
        if (mode.value === 'edit') {
          // 编辑模式 - 调用编辑API
          const params = {
            id: editId.value,
            contractTypeName: ruleForm.value.contractTypeName,
          }
          await editContractType(params)
          ElMessage.success('编辑成功！')
        } else if (mode.value === 'addChild') {
          // 新增子类型模式
          const params = {
            contractTypeName: ruleForm.value.contractTypeName,
            parentId: parentData.value?.id,
          }
          await addContractType(params)
          ElMessage.success('新增子类型成功！')
        } else {
          // 新增模式
          const params = {
            contractTypeName: ruleForm.value.contractTypeName,
          }
          await addContractType(params)
          ElMessage.success('新增成功！')
        }
        emit('refresh')
        close()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        loading.value = false
      }
    } else {
      console.log('表单验证失败:', fields)
    }
  })
}

// 暴露给模板的方法
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 12px;
}
.dialog-wrap {
  .footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
