.el-dialog {
  padding: 0 !important;

  // 标题
  .el-dialog__header {
    height: 47px;
    padding: 11px 16px !important;
    border-bottom: 1px solid #eaebf0 !important;

    // 名称
    .el-dialog__title {
      display: inline-block;
      height: 24px;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }

    // 关闭按钮
    .el-dialog__headerbtn {
      width: 47px;
      height: 47px;
      .el-dialog__close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: #a2a1a9;
        border: 1px solid #eaebf0;
        border-radius: 4px;
      }
      .el-dialog__close:hover {
        color: #a2a1a9;
        background-color: #f2f3f6;
        border: 1px solid;
        border-color: #eaebf0;
      }
    }
    .el-dialog__headerbtn:hover {
      .el-dialog__close {
        color: #a2a1a9;
      }
    }
  }
  .el-dialog__body {
    padding: 16px;
  }
  .el-dialog__footer {
    padding: 16px;
    border-top: 1px solid #eaebf0;
  }
  .el-button + .el-button {
    margin-left: 16px;
  }
}
