<script setup lang="ts">
const props = withDefaults(defineProps<{ tags?: string[]; label: string }>(), {
  label: '',
})

const emits = defineEmits(['update'])

const isEdit = ref(false)
const refCascader = ref()
const options = [
  {
    value: 'guide',
    label: 'Guide',
  },
  {
    value: '222222',
    label: '33333',
  },
  {
    value: 'guide112',
    label: 'Guide1',
  },
  {
    value: 'guide11',
    label: 'Guide2',
  },
]
const currentTags = ref([])

function handleEdit() {
  isEdit.value = true
  nextTick(() => {
    console.log('refCascader.value', refCascader.value)

    refCascader.value.togglePopperVisible()
  })
}
function handleBlur(e) {
  if (e === false) {
    isEdit.value = false
  }
  emits('update', currentTags.value)
  // isEdit.value = false
}
</script>

<template>
  <div class="item-wrap" ref="refContent">
    <span class="item-label">{{ label }}</span>
    <div class="item-content">
      <div v-if="!isEdit" class="content">{{ tags?.join(' ') }}</div>
      <el-cascader
        ref="refCascader"
        v-else
        v-model="currentTags"
        placeholder="Try searchingL Guide"
        :options="options"
        :props="{ multiple: true }"
        filterable
        v-on:change="isEdit = true"
        @visible-change="handleBlur"
      />
    </div>
    <el-button type="primary" link class="edit-btn" @click="handleEdit" v-if="!isEdit">编辑</el-button>
  </div>
  <!-- <div class="m-4">
    <p>标签</p>
    <el-cascader
      v-model="tags"
      placeholder="Try searchingL Guide"
      :options="options"
      :props="{ multiple: true }"
      filterable
    />
  </div> -->
</template>

<style lang="scss" scoped>
.item-wrap {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 300px;
  margin-bottom: 12px;
  .item-label {
    display: inline-block;
    width: 80px;
    padding: 8px 12px 0 0;
    font-size: 14px;
    text-align: right;
  }
  .item-content {
    position: relative;
    flex: 1;
    width: 0;
    overflow-y: auto;
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
  }
  .content {
    height: 100%;
    min-height: 31px;
    padding: 8px;
    line-height: 1.4;
    word-wrap: break-word;
  }
  .edit-btn {
    position: absolute;
    right: 12px;
    bottom: 4px;
    display: none;
  }
  &:hover .edit-btn {
    display: block;
  }
}
:deep(.el-cascader) {
  width: 100%;
}
:deep(.el-input__wrapper) {
  border: 0;
  box-shadow: none;
}
</style>
