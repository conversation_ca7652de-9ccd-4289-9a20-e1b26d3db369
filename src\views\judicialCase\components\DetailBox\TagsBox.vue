<script setup lang="ts">
import type { CascaderOption } from 'element-plus'
import type { IOption } from '../../types'

const props = withDefaults(defineProps<{ tags?: IOption[]; label: string; options: CascaderOption[] }>(), {
  label: '',
})

const emits = defineEmits(['update'])

const isEdit = ref(false)
const refCascader = ref()

const currentTags = ref<string[]>([])

function handleEdit() {
  isEdit.value = true
  nextTick(() => {
    refCascader.value.togglePopperVisible()
  })
}
function handleBlur(e: boolean) {
  if (e === false) {
    isEdit.value = false
  }
  const tempTags = currentTags.value.flat()
  const result = props.options.filter((item: CascaderOption) => {
    // 过滤掉数组中的空元素（如果有的话）
    if (!item || !item.value) return false
    // 检查当前元素的label是否在arr2中

    return tempTags.includes(String(item.value))
  })

  emits('update', result)
  // isEdit.value = false
}
</script>

<template>
  <div class="item-wrap" ref="refContent">
    <span class="item-label">{{ label }}</span>
    <div class="item-content">
      <div v-if="!isEdit" class="content">
        <el-tag v-for="(item, index) in tags" :key="index" class="tag">
          {{ item.label }}
        </el-tag>
      </div>
      <el-cascader
        ref="refCascader"
        v-else
        v-model="currentTags"
        placeholder="Try searchingL Guide"
        :options="options"
        :props="{ multiple: true }"
        filterable
        v-on:change="isEdit = true"
        @visible-change="handleBlur"
      />
    </div>
    <el-button type="primary" link class="edit-btn" @click="handleEdit" v-if="!isEdit">编辑</el-button>
  </div>
  <!-- <div class="m-4">
    <p>标签</p>
    <el-cascader
      v-model="tags"
      placeholder="Try searchingL Guide"
      :options="options"
      :props="{ multiple: true }"
      filterable
    />
  </div> -->
</template>

<style lang="scss" scoped>
.item-wrap {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 70vw;
  margin-bottom: 12px;
  overflow: hidden;
  .item-label {
    display: inline-block;
    width: 80px;
    padding: 8px 12px 0 0;
    font-size: 14px;
    text-align: right;
  }
  .item-content {
    position: relative;
    flex: 1;
    width: 0;
    overflow-y: auto;
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
  }
  .content {
    display: flex;
    gap: 0 8px;
    height: 100%;
    min-height: 31px;
    padding: 8px;
    line-height: 1.4;
    word-wrap: break-word;
  }
  .edit-btn {
    position: absolute;
    right: 12px;
    bottom: 4px;
    display: none;
  }
  &:hover .edit-btn {
    display: block;
  }
}
:deep(.el-cascader) {
  width: 100%;
}
:deep(.el-input__wrapper) {
  border: 0;
  box-shadow: none;
}
</style>
