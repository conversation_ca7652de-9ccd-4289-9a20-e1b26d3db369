<template>
  <div v-loading="loading" class="detail-wrap">
    <RuleDetails ref="refRuleDetails" class="rule-details" @save="save">
      <template #footer>
        <div class="page-footer">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </RuleDetails>
    <upload-dialog ref="refUploadDialog" @upload-rule="uploadRule"></upload-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, inject } from 'vue'
import { ElMessage } from 'element-plus'
import UploadDialog from './components/UploadDialog.vue'
import RuleDetails from './components/RuleDetails.vue'
import { saveBatchRuleItem } from '@/services/ruleBaseApi'

// 定义接口
interface RuleNode {
  id: string
  isSaved?: boolean
}

interface RuleObj {
  [key: string]: any
}

interface LeftTreeRef {
  getTreeData: () => void
}

interface RuleDetailsRef {
  getRuleData: (ruleNode: RuleNode) => void
  handleSaveData: () => void
}

interface UploadDialogRef {
  show: () => void
}

// 注入和响应式数据
const refLeftTree = inject<{ value: LeftTreeRef }>('refLeftTree')
const refRuleDetails = ref<RuleDetailsRef>()
const refUploadDialog = ref<UploadDialogRef>()
const loading = ref(false)

// 方法
const getRuleData = (ruleNode: RuleNode) => {
  refRuleDetails.value?.getRuleData(ruleNode)
}

function handleShowUpload() {
  refUploadDialog.value?.show()
}

function uploadRule() {
  refLeftTree?.value.getTreeData()
}

const handleSave = () => {
  refRuleDetails.value?.handleSaveData()
}

const save = async (ruleObj: RuleObj, ruleNode: RuleNode) => {
  const params = { ...ruleObj }
  params.ruleTypeId = ruleNode.id
  try {
    loading.value = true
    await saveBatchRuleItem([params])
    ruleNode.isSaved = true
    ElMessage.success('保存成功')
    // router.push({ name: 'RuleList' });
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  getRuleData,
  handleShowUpload,
  uploadRule,
})
</script>

<style lang="scss" scoped>
.detail-wrap {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 0;
  height: 100%;
  padding: 24px 0 0 32px;
  color: #262626;

  .rule-details {
    padding-right: 32px;

    .page-footer {
      display: flex;
      justify-content: center;
      padding: 20px 0 10px;
    }
  }
}
</style>
