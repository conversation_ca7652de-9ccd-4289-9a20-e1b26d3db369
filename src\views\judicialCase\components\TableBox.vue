<script setup lang="ts">
import { View as IconView } from '@element-plus/icons-vue'
import EditDialog from './EditDialog.vue'
import SummaryDialog from './SummaryDialog.vue'
import ViewScope from '@/components/ViewScope/index.vue'
import UploadDialog from './UploadDialog/index.vue'
import router from '@/router'
import { useCaseService } from './useCaseService'
import emitter, { EmitEvents } from '@/utils/emitter'
import useWindowResize from '@/composables/useWindowResize'
import type { ILawCaseSearchArgs } from '../types'
import type { LawCase } from './Model'
const { height } = useWindowResize()
const {
  tableData,
  total,
  pageSize,
  pageNum,
  loading,
  updateScope,
  search,
  handleDel,
  handleCurrentChange,
  handleSizeChange,
} = useCaseService()

const refEditDialog = ref<InstanceType<typeof EditDialog>>()
const refSummaryDialog = ref<InstanceType<typeof SummaryDialog>>()
const refUploadDialog = ref<InstanceType<typeof UploadDialog>>()
const refViewScope = ref()

const handleView = (id: string) => {
  router.push('/judicialCase/detail/' + id)
}

const handleViewSummary = (content: string) => {
  refSummaryDialog.value?.open(content)
}

const handleEdit = async (id: string) => {
  await refEditDialog.value!.open(id)
}

const selectedId = ref('')
const handleScope = (row: LawCase) => {
  selectedId.value = row.id
  refViewScope.value?.open(row.shareScope)
}
const setViewScope = (status: string) => {
  updateScope({ id: selectedId.value, shareScope: status })
}
const handleAgain = (id: string) => {
  console.log(id)
}

const handleExport = () => {
  refUploadDialog.value?.open('aa')
}

onMounted(() => {
  search()
  maxHeight.value = height.value - 330

  emitter.on(EmitEvents.SEARCHBOX_EXPAND, (flag: any) => {
    if (!flag) {
      maxHeight.value = height.value - 240
    } else {
      maxHeight.value = height.value - 330
    }
  })
})

onUnmounted(() => {
  emitter.off(EmitEvents.SEARCHBOX_EXPAND)
})

const getSubStr = (str: string, length: number) => {
  if (!str) {
    return ''
  }
  if (str.length <= length) {
    return str
  }
  return str.substring(0, length) + '...'
}
const simplifyDate = (date: string) => {
  if (!date) {
    return ''
  }
  return date.indexOf('T') ? date.split('T')[0] : date
}
const maxHeight = ref(400)

defineExpose({
  query(args: ILawCaseSearchArgs) {
    search(args)
  },
})
</script>

<template>
  <div class="table-wrap">
    <div class="top-handle"><el-button class="upload-btn" type="primary" @click="handleExport">导入</el-button></div>
    <el-table
      ref="table"
      v-loading="loading"
      :max-height="maxHeight"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column label="案号" width="200">
        <template #default="scope">
          <span>{{ scope.row.caseNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="案例名称" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.caseName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="案由" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.caseReason || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="案件摘要" width="300">
        <template #default="{ row }">
          <el-popover effect="dark" trigger="hover" placement="top" width="auto">
            <template #default>
              <div>{{ row.caseSummary }}</div>
            </template>
            <template #reference>
              <el-link @click="handleViewSummary(row.caseSummary)">
                {{ getSubStr(row.caseSummary, 16) }}
                <el-icon class="el-icon--right"><icon-view /></el-icon>
              </el-link>
            </template>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="裁判日期" width="100">
        <template #default="scope">
          <span>{{ simplifyDate(scope.row.judgeTimeStr) || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="适用地域" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.caseLocation }}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="创建人">
        <template #default="scope">
          <span>{{ scope.row.createBy }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="可见范围">
        <template #default="scope">
          <span>{{ scope.row.shareScope === 100 ? '公开可见' : scope.row.shareScope === 0 ? '后台可见' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据来源">
        <template #default="scope">
          <span>{{ scope.row.originTypeName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button-group class="el-button-right-line">
            <el-button type="primary" link @click="handleView(row.id)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(row.id)">编辑</el-button>
            <el-button type="primary" link @click="handleDel(row.id)">删除</el-button>
            <el-button type="primary" link @click="handleScope(row)">可见范围</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="pageNum"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
    <EditDialog ref="refEditDialog" @close="search" />
    <SummaryDialog ref="refSummaryDialog" />
    <UploadDialog ref="refUploadDialog" @close="search" />
    <ViewScope ref="refViewScope" @close="setViewScope" />"
  </div>
</template>

<style lang="scss" scoped>
.table-wrap {
  position: relative;
  flex: 1;
  padding-bottom: 42px;

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 12px;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }
  .top-handle {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding-right: 16px;
    .upload-btn {
      width: 80px;
    }
  }
}
</style>
