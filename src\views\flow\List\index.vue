<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'
import ToolBar from './components/ToolBar.vue'
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import type { IWorkflowQueryData } from '../api/index.ts'

const tableBoxRef = ref<InstanceType<typeof TableBox>>()
const updateTable = (searchParams?: IWorkflowQueryData) => {
  tableBoxRef.value?.getAllFlows(searchParams)
}
</script>

<template>
  <PageLayout>
    <template #headerName>
      <span class="header-name">流程管理</span>
    </template>
    <template #headerBtn>
      <ToolBar @updateData="updateTable" />
    </template>
    <template #search>
      <SearchBox @search="updateTable"></SearchBox>
    </template>
    <template #table>
      <TableBox ref="tableBoxRef" />
    </template>
  </PageLayout>
</template>
