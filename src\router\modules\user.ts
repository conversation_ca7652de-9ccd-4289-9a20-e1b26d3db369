import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  // 用户管理
  {
    path: '/',
    component: Layout,
    meta: {
      title: '用户管理',
      custom: true,
    },
    children: [
      {
        path: '/user',
        name: 'User',
        component: () => import(/* webpackChunkName: "home" */ '@/views/user/index.vue'),
      },
    ],
  },
]
export default router
