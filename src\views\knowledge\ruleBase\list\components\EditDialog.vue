<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    :title="title"
    append-to-body
    :close-on-click-modal="false"
    width="480px"
    show-close
    @close="close"
  >
    <el-form ref="refForm" label-width="110px" :model="ruleForm" :rules="rules">
      <el-form-item label="清单名称" prop="name">
        <el-input v-model="ruleForm.name" show-word-limit maxlength="50" clearable placeholder="请输入清单名称" />
      </el-form-item>
      <el-form-item v-if="showStandpoint" label="审查立场" prop="standpoint">
        <el-input v-model="ruleForm.standpoint" show-word-limit maxlength="30" clearable placeholder="请输入审查立场" />
      </el-form-item>
      <el-form-item label="适用合同类型" prop="contractType">
        <el-tree-select
          v-model="ruleForm.contractType"
          :data="props.contractTypeTreeData"
          :props="{ label: 'contractTypeName', value: 'id' }"
          placeholder="请选择适用合同类型"
          style="width: 100%"
          filterable
          check-strictly
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="ruleForm.remark"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :maxlength="200"
          show-word-limit
          clearable
          placeholder="请输入备注"
        />
      </el-form-item>
      <div class="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleCommit">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { saveRuleList } from '@/services/ruleBaseApi'
import { ElMessage } from 'element-plus'

// Props 定义
const props = defineProps<{
  contractTypeTreeData: any[]
}>()

const emit = defineEmits<{
  refresh: []
}>()

const rules = reactive({
  name: [
    { required: true, message: '请输入清单名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度限制在2-50个字', trigger: 'blur' },
  ],
  standpoint: [
    { required: true, message: '请输入审查立场', trigger: 'blur' },
    { min: 2, max: 30, message: '长度限制在2-30个字', trigger: 'blur' },
  ],
  contractType: [{ required: true, message: '请选择适用合同类型', trigger: 'blur' }],
  globallyVisibleFlag: [{ required: true, message: '请选择授权类型', trigger: 'change' }],
  visibleUsers: [{ required: true, message: '请选择可见范围', trigger: 'change' }],
})

const ruleId = ref<string>()
const visible = ref(false)
const showStandpoint = ref(true)
const refForm = ref<any>(null)

// 定义用户类型接口
interface User {
  userCode: string
  realName: string
}

// 记录临时选中的可见范围用户
const tempUserList = ref<User[]>([])

// 移除本地合同类型数据定义，改为使用 props

function iniDataFn() {
  return {
    name: '',
    standpoint: '',
    remark: '',
    globallyVisibleFlag: true,
    visibleUsers: [],
    contractType: '', // 单选，使用字符串而不是数组
  }
}

const ruleForm = ref(iniDataFn())
const title = computed(() => {
  return ruleId.value ? '编辑' : '新建' + '审查清单'
})

async function open(row: any) {
  if (row.id) {
    tempUserList.value = row.visibleUsers
    ruleId.value = row.id
    ruleForm.value.name = row.ruleListName
    ruleForm.value.standpoint = row.rulePosition
    ruleForm.value.remark = row.ruleRemark
    ruleForm.value.globallyVisibleFlag = row.globallyVisibleFlag
    ruleForm.value.visibleUsers = row.visibleUsers?.map((item: any) => item.userCode)
    if (row.id && !row.rulePosition) {
      showStandpoint.value = false
    }
  }
  visible.value = true
}

function close() {
  ruleId.value = ''
  visible.value = false
  showStandpoint.value = true
  // refForm.value.resetFields();
  ruleForm.value = iniDataFn()
  refForm.value?.resetFields()
  refForm.value?.clearValidate()
}

function handleCommit() {
  refForm.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      const params = {
        id: ruleId.value || undefined,
        ruleListName: ruleForm.value.name,
        rulePosition: ruleForm.value.standpoint,
        ruleRemark: ruleForm.value.remark,
        globallyVisibleFlag: ruleForm.value.globallyVisibleFlag,
        visibleUsers: ruleForm.value.visibleUsers,
      }
      await saveRuleList(params)
      ElMessage.success('创建成功！')
      console.log('Save params:', params)
      emit('refresh')
      close()
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 合同类型数据已在父组件挂载时加载

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.input {
  width: 362px;
  margin-left: 38px;
}
.footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-top: 40px;
}
:deep(.el-dialog__body) {
  padding-bottom: 12px;
}
</style>
