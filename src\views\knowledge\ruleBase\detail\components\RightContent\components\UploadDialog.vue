<template>
  <el-dialog v-loading="loading" title="导入规则列表" v-model="visible" :close-on-click-modal="false" width="480px">
    <div class="item">
      <span class="item-txt">1、下载Excel模板，填写信息。</span
      ><span class="item-download" @click="handleDownload">下载空的模板表格 <i class="icon-fdd-xiazai"></i></span>
    </div>
    <div class="item">
      <span class="item-txt">2、请先保存已填写的规则，再上传填写好的信息。</span>
      <el-upload
        action="#"
        :auto-upload="false"
        :show-file-list="false"
        :accept="acceptList.join(',')"
        :on-change="changeUpload"
      >
        <el-button type="primary">上传规则列表</el-button>
      </el-upload>
    </div>
    <div class="item"><span class="item-txt">3、上传完成，信息自动填充。</span></div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'
import { uploadRuleFile, downloadRuleTemp } from '@/services/ruleBaseApi'

// 定义 emits
const emit = defineEmits<{
  'upload-rule': []
}>()

// 获取路由参数
const route = useRoute()
const ruleListId = route.query.id as string

// 响应式数据
const loading = ref(false)
const visible = ref(false)

// 方法
function show() {
  visible.value = true
}

function close() {
  visible.value = false
}

const acceptList = ['.xls', '.xlsx']

async function changeUpload(file: UploadFile) {
  if (file.size && file.size > 100 * 1024 * 1024) {
    return ElMessage.error(`上传文件不能大于${100}M：${file.name}`)
  }

  let fileSuffix = file.name.substring(file.name.lastIndexOf('.')) || ''
  fileSuffix = fileSuffix.toLowerCase()
  if (acceptList.join('、').indexOf(fileSuffix) === -1) {
    return ElMessage.error(`上传文件只能是 ${acceptList.join('、')}格式：${file.name}`)
  }

  const formdata = new FormData()
  formdata.append('file', file.raw as File)
  formdata.append('ruleListId', ruleListId)
  loading.value = true
  try {
    await uploadRuleFile(formdata)
    loading.value = false
    emit('upload-rule')
    close()
  } catch (error) {
    loading.value = false
    console.warn(error)
  }
}

function handleDownload() {
  downloadRuleTemp()
    .then((response) => {
      // 处理下载响应
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '规则导入模板.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)
    })
    .catch((error) => {
      console.warn('下载失败:', error)
      ElMessage.error('下载失败')
    })
}

// 暴露方法给父组件
defineExpose({
  show,
  close,
})
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
  font-size: 12px;
  color: #000;

  &:last-child {
    margin-bottom: 0;
  }

  &-txt {
    display: flex;
    margin-right: 10px;
  }

  &-download {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--main-bg);
    cursor: pointer;

    .icon-fdd-xiazai {
      font-size: 14px;
    }
  }
}
</style>
