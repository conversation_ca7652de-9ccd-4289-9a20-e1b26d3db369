export function getQueryPath(name: string): string {
  const fullPath = location.search
  if (!fullPath.split(`${name}=`)[1]) return ''
  return decodeURIComponent(fullPath.split(`${name}=`)[1])
}

/**
 * 深度克隆对象、数组
 * @param obj 被克隆的对象
 * @return 克隆后的对象
 */
export function cloneObject(obj: any) {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * 深拷贝
 * @param {*} obj
 * @param {*} hash
 */
export function deepClone(obj: any, hash = new WeakMap()) {
  if (obj === null) return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof RegExp) return new RegExp(obj)
  if (typeof obj !== 'object') return obj
  if (hash.get(obj)) return obj
  const cloneObj = new obj.constructor()
  hash.set(obj, cloneObj)
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloneObj[key] = deepClone(obj[key], hash)
    }
  }
  return cloneObj
}