<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import { useGeneralService } from '@/composables/useGeneralService'
import { getLawById, updateLawInfo } from '@/services/regulations'
import type { ILawRecord } from '../types'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
const { tagsData, queryTagsList, TagBizType } = useGeneralService()
const visible = ref(false)
const emit = defineEmits(['close'])
const loadDetail = async (id: string) => {
  const { code, message, data } = await getLawById<ILawRecord>(id)
  if (code === RESPONSE_CODE_SUCCESS) {
    const { lawName, lawDegree, currentValid, labels, originType } = data as ILawRecord

    formData.value = {
      id: id,
      lawName,
      lawDegree,
      currentValid: currentValid ? currentValid : false,
      labelIds: labels.map((item: any) => item.id),
      originType,
    }
  } else {
    ElMessage.error(message)
  }
}
async function open(id: string) {
  refForm.value?.clearValidate()
  refForm.value?.resetFields()
  visible.value = true
  await queryTagsList(String(TagBizType.LAW))

  await loadDetail(id)
}

function close() {
  visible.value = false
}

const refForm = ref<FormInstance>()

const formData = ref({
  id: '',
  originType: '',
  lawName: '',
  lawDegree: '',
  currentValid: false,
  labelIds: [] as string[],
})

const rules = {
  lawName: [{ required: true, message: '请输入法律名称', trigger: 'blur' }],
  lawDegree: [{ required: true, message: '请选择效率级别', trigger: 'change' }],
  currentValid: [{ required: true, message: '请选择法时效性', trigger: 'change' }],
}
const uppdate = async (postData: any) => {
  const { code, message } = await updateLawInfo(postData)
  if (code === RESPONSE_CODE_SUCCESS) {
    emit('close')
    close()
  } else {
    ElMessage.error(message)
  }
}
function handleCommit() {
  refForm.value?.clearValidate()
  refForm.value?.validate((valid) => {
    if (valid) {
      uppdate(formData.value)
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="编辑" v-model="visible" :close-on-click-modal="false" width="500px">
      <el-form ref="refForm" label-width="110px" :model="formData" :rules="rules">
        <el-form-item label="法律名称" prop="lawName">
          <el-input v-model="formData.lawName" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="效率级别" prop="lawDegree">
          <el-input
            v-model="formData.lawDegree"
            show-word-limit
            maxlength="30"
            clearable
            placeholder="请输入效率级别"
          />
        </el-form-item>
        <el-form-item label="时效性" prop="currentValid">
          <el-select v-model="formData.currentValid" placeholder="请选择时效性" style="width: 100%">
            <el-option :value="true" label="有效" />
            <el-option :value="false" label="无效" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="labelIds">
          <el-select v-model="formData.labelIds" multiple placeholder="请选择标签" style="width: 100%">
            <el-option v-for="item in tagsData" :key="item.id" :label="item.labelName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
