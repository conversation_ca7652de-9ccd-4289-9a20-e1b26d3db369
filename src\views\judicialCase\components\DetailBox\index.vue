<script setup lang="ts">
import type { ILawCase, IOption } from '../../types'
import ItemBox from './ItemBox.vue'
import TagsBox from './TagsBox.vue'
import { getLawCaseById, updateLawCase } from '@/services/judicialCase'
import { useRoute } from 'vue-router'
import { useTagService } from '@/views/useTagsService'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
const { queryTagsList, tagsData } = useTagService()
const route = useRoute()
const keyMap: any = {
  caseNumber: '案号',
  caseName: '案件名称',
  caseReason: '案由',
  caseSummary: '案例概要',
  courtName: '审理法院',
  casePoint: '争议焦点',
  courtEvidence: '本院查明',
  courtViewPoint: '本院认为',
  judgeResults: '判决结果',
  judgePoint: '判决要旨',
  lawSuit: '法律适用',
  labelIds: '标签',
}
interface IFormItem {
  label: string
  text: string
  key: string
}
const list = ref<IFormItem[]>([])

const options = ref<IOption[]>([])

function handleUpdate(txt: string, item: IFormItem) {
  if (!txt || txt.trim() === '') {
    return
  }

  item.text = txt
  const params: any = {}
  params[item.key] = txt
  update(params)
  // list.value[idx].text = txt
}

const tags = ref<IOption[]>()
function handleUpdateTags(e: IOption[]) {
  if (Array.isArray(e)) {
    tags.value = e
    const ids = tags.value.map((item: IOption) => item.value)
    update({ labelIds: ids })
  }

  console.log('updateData', e)
}
const update = async (updateData: any) => {
  const { code, message } = await updateLawCase({ id: caseId.value, ...updateData })
  if (code === RESPONSE_CODE_SUCCESS) {
  } else {
    ElMessage.error(message)
  }
}

const caseId = ref('')

const getDetail = async (id: string) => {
  caseId.value = id
  const { code, data, message } = await getLawCaseById<ILawCase>(id)
  const temp = data as any
  Object.keys(temp).forEach((key) => {
    if (keyMap[key]) {
      list.value.push({
        key: key,
        label: keyMap[key],
        text: temp[key] ? temp[key] : '',
      })
    }
  })
}
onMounted(async () => {
  const { id } = route.params

  getDetail(id as string)
  await queryTagsList()
  options.value = tagsData.value.map((item) => {
    return {
      label: item.labelName,
      value: item.id,
    }
  })
})
</script>
<template>
  <el-scrollbar height="calc(100vh - 80px)">
    <div class="detail-wrap">
      <template v-for="(item, idx) of list" :key="idx">
        <ItemBox :text="item.text" :label="item.label" @update="(txt: string) => handleUpdate(txt, item)" />
      </template>
      <TagsBox :tags="tags" :options="options" :label="'标签'" @update="(e: IOption[]) => handleUpdateTags(e)" />
    </div>
  </el-scrollbar>
</template>

<style lang="scss" scoped>
.detail-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  padding-top: 2rem;
  padding-left: 2rem;
  overflow: auto;
  text-align: left;
}
</style>
