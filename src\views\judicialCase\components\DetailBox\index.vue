<script setup lang="ts">
import ItemBox from './ItemBox.vue'
import TagsBox from './TagsBox.vue'

const list = ref([
  { label: '案号', text: 'aaaa' },
  { label: '案名', text: 'bbbb' },
])

function handleUpdate(e: string, idx: number) {
  list.value[idx].text = e
}

const tags = ref<string[]>()
function handleUpdateTags(e: string[]) {
  tags.value = e
}
</script>
<template>
  <div class="detail-wrap">
    <template v-for="(item, idx) of list" :key="idx">
      <ItemBox :text="item.text" :label="item.label" @update="(e: string) => handleUpdate(e, idx)" />
    </template>
    <TagsBox :tags="tags" :label="'标签'" @update="(e: string) => handleUpdateTags(e, idx)" />
  </div>
</template>

<style lang="scss" scoped>
.detail-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-top: 16px;
}
</style>
