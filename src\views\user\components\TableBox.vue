<script setup lang="ts">
import SaveUserDialog from './SaveUserDialog.vue'
import { queryPageUserList, updateUserStatus, resetPassword } from '@/services/user.ts'
import type { IQueryPageUserList, IUser, IUserQueryData, IUpdateUserStatusParams } from '@/services/user.ts'

const saveUserDialogRef = ref<InstanceType<typeof SaveUserDialog>>()
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

const list = ref<IUser[]>()

// 分页获取用户列表
const getPageUserList = async (searchParams?: IUserQueryData) => {
  loading.value = true
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      data: searchParams || {},
    }
    const { data } = await queryPageUserList<IQueryPageUserList>(params)
    list.value = data.list || []
    pageNum.value = data.pageNum
    pageSize.value = data.pageSize
    total.value = +data.total
  } catch (err) {
    console.log('获取用户列表失败', err)
  } finally {
    loading.value = false
  }
}

// 编辑用户
const editUser = (row: IUser) => {
  saveUserDialogRef.value!.openDialog(row)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNum.value = 1
  getPageUserList()
}
const handleCurrentChange = (val: number) => {
  pageNum.value = val
  getPageUserList()
}

// 初始化
onMounted(() => {
  getPageUserList()
})

defineExpose({
  getPageUserList,
})

// 启用/禁用用户
const handleChangeStatus = async (e: boolean, row: IUser) => {
  if (e === !!row.userStatus) return
  const params: IUpdateUserStatusParams = {
    id: row.id,
    userStatus: e ? 1 : 0, // 1-启用，0-禁用
  }
  const res = await updateUserStatus(params)
  if (res.code === '000000') {
    ElMessage.success(e ? '启用成功' : '禁用成功')
    await getPageUserList()
  } else {
    ElMessage.error(e ? '启用失败' : '禁用失败')
  }
}

// 重置密码
const resetPwd = (id: string) => {
  ElMessageBox.confirm('确定要重置该用户密码吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      const res = await resetPassword(id)
      console.log(res)
      ElMessage.success('密码已重置为默认值')
    })
    .catch((error) => {
      console.error('重置密码操作被取消', error)
      // Handle the cancellation action here
    })
}
</script>

<template>
  <div class="table-body">
    <el-table :data="list" row-key="id" v-loading="loading" max-height="calc(100vh - 210px)">
      <el-table-column prop="realName" label="用户名称" />
      <el-table-column prop="userName" label="登录账号" />
      <el-table-column label="状态">
        <template #default="scope">
          <el-switch
            :model-value="!!scope.row.userStatus"
            @change="(e) => handleChangeStatus(e as boolean, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button type="primary" link @click="editUser(scope.row)">编辑用户</el-button>
          <el-button type="primary" @click="resetPwd(scope.row.id)" link>重置密码</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="table-footer">
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 40]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <SaveUserDialog ref="saveUserDialogRef" @updateList="getPageUserList" />
</template>

<style scoped lang="scss">
////////// 表格高度自适应待处理
.table-body {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
.table-footer {
  flex-shrink: 0;
}
</style>
