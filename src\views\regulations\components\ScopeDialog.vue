<script lang="ts" setup>
const visible = ref(false)
const scope = ref('')
let aResolve: (value: unknown) => void

function open() {
  visible.value = true
  return new Promise((resolve) => {
    aResolve = resolve
  })
}

function close(flag?: boolean) {
  visible.value = false
  aResolve(!!flag)
}

function handleCommit() {
  close(true)
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="设置可见范围" v-model="visible" :close-on-click-modal="false" width="500px">
      <el-select v-model="scope" placeholder="请选择失效性" style="width: 100%">
        <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
