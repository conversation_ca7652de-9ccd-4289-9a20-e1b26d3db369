import { showDetail, clearDetail } from './box-dialog'

let hoverTimer: number | null = null
let currentTargetId: string | number | undefined
let nodeIdx = 1

const hoverTips = {
  // Vue 3 中 bind 改为 mounted
  mounted: function (targetEl: HTMLElement, binding: any) {
    // Vue 3 中不再有 vnode.key，我们可以使用其他方式
    const uniqueId = +new Date() + '' + nodeIdx
    nodeIdx += 1
    targetEl.dataset.hoverTipsId = uniqueId

    targetEl.onmouseover = function (mouseEvt: MouseEvent) {
      if (hoverTimer) clearTimeout(hoverTimer)

      const { moveIn, node } = binding.value

      if (moveIn && typeof moveIn === 'function') moveIn(node, mouseEvt, targetEl)
      else {
        if (hoverTimer && currentTargetId !== binding.value.node.id) {
          clearDetail()
        }
        // if (!binding.value.node.cooperationUserNum) return;
        const position = {
          left: mouseEvt.pageX - targetEl.offsetLeft + parseInt(targetEl.getBoundingClientRect().width.toString()),
          top: mouseEvt.pageY - targetEl.offsetTop,
        }
        const node = Object.assign({}, binding.value.node, position)
        showDetail({ node }, targetEl)
        currentTargetId = binding.value.node.id
      }
    }
    targetEl.onmouseout = function (e: MouseEvent) {
      const { moveOut } = binding.value
      hoverTimer = setTimeout(() => {
        if (moveOut && typeof moveOut === 'function') moveOut()
        else clearDetail()
      }, 50)
    }
  },
  // Vue 3 中 unbind 改为 unmounted
  unmounted: function (targetEl: HTMLElement) {
    targetEl.onmouseover = null
    targetEl.onmouseout = null
    if (hoverTimer) {
      clearTimeout(hoverTimer)
      hoverTimer = null
    }
  }
}

export default hoverTips
