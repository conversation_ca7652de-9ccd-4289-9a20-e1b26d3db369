import type { ILawCase } from '../types'

export class LawCase implements ILawCase {
  labelIds: string[] = []
  shareScope = 0
  createBy = ''
  updateBy = ''
  createTime = ''
  updateTime = ''
  corpId = ''
  createByName = ''
  updateByName = ''
  corpName = ''
  id = ''
  caseNumber = ''
  caseName = ''
  caseSummary = ''
  casePoint = ''
  caseReason = ''
  caseEvaluate = ''
  caseLocation = ''
  lawSuit = ''
  courtName = ''
  courtViewPoint = ''
  courtEvidence = ''
  judgeResults = ''
  judgePoint = ''
  judgeTime = ''
  judgeTimeStr = ''
}
