import type { ILawCase } from '../types'

export class LawCase implements ILawCase {
  createBy = ''
  updateBy = ''
  createTime = ''
  updateTime = ''
  corpId = ''
  createByName = ''
  updateByName = ''
  corpName = ''
  id: number = 0
  caseNumber = ''
  caseName = ''
  caseSummary = ''
  casePoint = ''
  caseReason = ''
  caseEvaluate = ''
  caseLocation = ''
  lawSuit = ''
  courtName = ''
  courtViewPoint = ''
  courtEvidence = ''
  judgeResults = ''
  judgePoint = ''
  judgeTime = ''
  judgeTimeStr = ''
}

export class TagItem {
  id = ''
  labelName = ''
  bizType = ''
  bizTypeName = ''
  enableStatus = false
  corpName = ''
}
