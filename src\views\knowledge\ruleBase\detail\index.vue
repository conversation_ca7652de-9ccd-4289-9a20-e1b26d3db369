<template>
  <div class="page-wrap-box">
    <ProgressLoader
      :check-progress="fetchAnalysisProgress"
      :check-params="ruleListId"
      :fake-update-interval="1000"
      @complete="handleLoadingComplete"
    >
      <HeaderBox isShowBack>
        <template #header-title>
          <div class="page-wrap-header">
            <span>{{ ruleListName }}(审查立场：{{ rulePosition || '-' }})</span>
          </div>
        </template>
        <template #option>
          <span>
            <el-button type="default" @click="handleShowAdd">添加规则</el-button>
            <el-button type="primary" @click="handleShowUpload">上传规则列表</el-button>
          </span>
        </template>
      </HeaderBox>

      <div class="page-wrap">
        <LeftTree ref="refLeftTree" :style="{ width: `${width}px` }" />
        <ResizeBar @mousedown="handleMouseDown" />
        <RightContent ref="refContent" :style="{ width: `calc(100% - ${width}px - 6px)` }" />
      </div>
      <AddNewRules ref="refNewRulesDialog" @addBatchRuleItem="addBatchRuleItem"></AddNewRules>
    </ProgressLoader>
  </div>
</template>

<script setup lang="ts">
import { ref, provide } from 'vue'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import LeftTree from './components/LeftTree/index.vue'
import RightContent from './components/RightContent/index.vue'
import AddNewRules from './components/AddNewRules/index.vue'
import { getReviewDetails, getAnalysisProgress } from '@/services/ruleBaseApi'
import { useResizeBar } from '@/composables/useResizeBar.ts'
import ResizeBar from '@/components/ResizeBar.vue'
import ProgressLoader from '@/components/ProgressLoader.vue'

// 获取路由信息
const route = useRoute()
const ruleListId = route.query.id as string
const ruleListName = ref('')
const rulePosition = ref('')

// 组件引用
const refContent = ref()
provide('refContent', refContent)

const refLeftTree = ref()
provide('refLeftTree', refLeftTree)

const refNewRulesDialog = ref()

// 提供给子组件的数据
provide('ruleListName', ruleListName)

// 获取详情数据
const getDetailsData = async () => {
  const { data } = await getReviewDetails(ruleListId)
  ruleListName.value = data.ruleListName
  rulePosition.value = data.rulePosition
}

// 获取分析进度
const fetchAnalysisProgress = async () => {
  return await getAnalysisProgress(ruleListId)
}

// 页面拖拽逻辑
const { width, handleMouseDown } = useResizeBar({
  initialWidth: 300,
  minWidth: 200,
  maxWidth: () => window.innerWidth - 400,
  storageKey: 'leftPaneWidth',
})

// 加载完成后刷新树数据
const handleLoadingComplete = () => {
  refLeftTree.value?.getTreeData()
}

// 上传规则列表弹窗
const handleShowUpload = () => {
  refContent.value?.handleShowUpload()
}

// 添加规则弹窗
const handleShowAdd = () => {
  refNewRulesDialog.value?.show()
}

// 添加规则成功后，刷新左侧列表
const addBatchRuleItem = () => {
  refLeftTree.value?.getTreeData()
}

// 初始化数据
getDetailsData()
</script>

<style lang="scss" scoped>
.page-wrap-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  .page-wrap-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .page-wrap {
    position: relative;
    display: flex;
    height: 100%;
    height: calc(100vh - 60px);
    .placeholder {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #999;
      background-color: #f5f5f5;
    }
  }
}
</style>
