<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import SaveUserDialog from './components/SaveUserDialog.vue'
import type { IUserQueryData } from '@/services/user.ts'

const tableBoxRef = ref<InstanceType<typeof TableBox>>()
const updateTable = (searchParams?: IUserQueryData) => {
  tableBoxRef.value!.getPageUserList(searchParams)
}

const saveUserDialogRef = ref<InstanceType<typeof SaveUserDialog>>()

const addUser = () => {
  saveUserDialogRef.value?.openDialog()
}
</script>

<template>
  <PageLayout>
    <template #headerName>
      <span class="header-name">用户管理</span>
    </template>
    <template #operation>
      <el-button type="primary" @click="addUser">
        <el-icon class="add-icon"><Plus /></el-icon>
        创建用户
      </el-button>
    </template>
    <template #search>
      <SearchBox @search="updateTable"></SearchBox>
    </template>
    <template #table>
      <TableBox ref="tableBoxRef" />
    </template>
  </PageLayout>
  <SaveUserDialog ref="saveUserDialogRef" @updateList="updateTable" />
</template>

<style lang="scss" scoped>
.add-icon {
  margin-right: 4px;
  font-weight: 700;
}
</style>
