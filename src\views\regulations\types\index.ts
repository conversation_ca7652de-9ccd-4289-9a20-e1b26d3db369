// enum sourceEnum {
//   MANUAL = 0, // 排队中
//   AUTO = 1,
// }

// enum lowEnum {
//   LAW = 1,
//   ADMIN = 2,
//   DEPART = 3,
//   JUDICAL =4
//   PLACE = 5
// }

// enum timelinessEnum = {
//   NOW = 1
// }

export interface ILawSearchArgs {
  lawName?: string //法律名称
  currentValid?: boolean //实效性
  lawDegree?: string //法律级别
  labelIds?: string[] //标签
  scope?: string //可见范围
  originType?: string //数据来源
}

export interface ILawParams {
  pageNum: number
  pageSize: number
  data: Partial<ILawSearchArgs>
}

export interface ILawRecord {
  id: string
  lawName: string
  lawDegree: number
  currentValid: boolean
  labelIds: string[]
  labels: any[]
  originType: string
  createBy?: string
  updateBy?: string
  createTime?: string
  updateTime?: string
  corpId?: string
  createByName?: string
  updateByName?: string
  corpName?: string
  lawPreface?: string
  publishDate?: string
  lawArticleNo?: string
  executeDate?: string
}

export interface ILawResponse {
  total: number
  list: ILawRecord[]
}

export interface ILawTableParams {
  page: number
  pageSize: number
  data: { content?: string }
}

export interface ILawTableRecord {
  lawName: string
  book: string
  chapter: string
  festival: string
  terms: string
  tag: string
}

export interface ILawTableResponse {
  total: number
  list: ILawTableRecord[]
}
