import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  // 流程管理
  {
    path: '/',
    component: Layout,
    meta: {
      title: '工作流',
      custom: true,
    },
    children: [
      {
        path: '/flow',
        name: 'FlowManage',
        component: () => import(/* webpackChunkName: "FlowManage" */ '@/views/flow/List/index.vue'),
      },
    ],
  },
  // 流程详情
  {
    path: '/',
    meta: {
      title: '流程详情',
    },
    children: [
      {
        path: '/flowinfo',
        name: 'WorkFlow',
        component: () => import(/* webpackChunkName: "WorkFlow" */ '@/views/flow/WorkFlow/index.vue'),
      },
    ],
  },
  // 模型列表
  {
    path: '/',
    component: Layout,
    meta: {
      title: '模型',
      custom: true,
    },
    children: [
      {
        path: '/model',
        name: 'Model',
        component: () => import(/* webpackChunkName: "Model" */ '@/views/model/index.vue'),
      },
    ],
  },
]
export default router
