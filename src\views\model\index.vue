<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'
import ToolBar from './components/ToolBar.vue'
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import { queryModelTypeList } from './api/index.ts'
import type { IModelType, IModelQueryData } from './api/index.ts'

const modelTypeList = ref<IModelType[]>([])

// 获取模型分类列表
const getModelTypeList = async () => {
  try {
    const { data } = await queryModelTypeList<IModelType[]>()
    modelTypeList.value = data || []
  } catch (err) {
    console.log('获取模型分类列表失败', err)
  }
}

const tableBoxRef = ref<InstanceType<typeof TableBox>>()
const updateTable = (searchParams?: IModelQueryData) => {
  tableBoxRef.value?.getPageModelList(searchParams)
}

onMounted(async () => {
  await getModelTypeList()
})
</script>

<template>
  <PageLayout>
    <template #headerName>
      <span class="header-name">模型列表</span>
    </template>
    <template #headerBtn>
      <ToolBar @updateData="updateTable" :model-type-list="modelTypeList" />
    </template>
    <template #search>
      <SearchBox @search="updateTable" :model-type-list="modelTypeList"></SearchBox>
    </template>
    <template #table>
      <TableBox ref="tableBoxRef" :model-type-list="modelTypeList" />
    </template>
  </PageLayout>
</template>
