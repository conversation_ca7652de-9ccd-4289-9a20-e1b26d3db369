import request from '@/services'
import type { IResponse } from '@/services'
function post<T, U>(url: string, data: U): Promise<IResponse<T>> {
  return request({
    url: url,
    method: 'post',
    data,
  })
}

function get<T, U>(url: string, params?: U): Promise<IResponse<T>> {
  return request({
    url: url,
    method: 'get',
    params,
  })
}

function del<T, U>(url: string, params?: U): Promise<IResponse<T>> {
  return request({
    url: url,
    method: 'delete',
    params,
  })
}

const upload = <T>(url: string, data?: any, config?: any): Promise<IResponse<T>> =>
  request({
    url,
    method: 'post',
    data,
    ...config,
  })

/**
 * 导入案例
 * @param data
 * @returns
 */
export function importCase<T, U>(data: U): Promise<IResponse<T>> {
  return post('/lawCase/import', data)
}

/**
 * 查询案例列表
 * @param data
 * @returns
 */
export function getLawCaseList<T, U>(data: U): Promise<IResponse<T>> {
  return post('/lawCase/page', data)
}

/**
 * 获取案例ById
 */
export function getLawCaseById<T>(id: string): Promise<IResponse<T>> {
  return get(`/lawCase/detail/${id}`)
}

/**
 * 更新案例数据
 * @param data
 * @returns
 */
export function updateLawCase<T>(data: any): Promise<IResponse<T>> {
  return post('/lawCase/update', data)
}

export function delLawCaseById<T>(id: string): Promise<IResponse<T>> {
  return del(`/lawCase/delete/${id}`)
}
/**
 * 上传文件
 * @param {*} data
 * @param {*} conf
 */
export function uploadFileTemporary<T>(data: any, conf: any): Promise<IResponse<T>> {
  return upload('/attachment/upload-temporary', data, { ...conf })
}

/**
 * 获取标签
 * @param {*} data
 */
export function getTagsByBizCode<T>(bizCode: string): Promise<IResponse<T>> {
  return get('/label/bizLabels', { bizCode })
}
