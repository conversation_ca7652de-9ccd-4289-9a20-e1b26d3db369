<template>
  <el-select
    v-model="selectedValues"
    :placeholder="placeholder"
    :disabled="disabled"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :multiple-limit="multipleLimit"
    popper-append-to-body
    @visible-change="handleVisibleChange"
  >
    <template #default>
      <!-- 选项列表 -->
      <el-option
        v-for="item in currentPageData"
        :key="item[valueKey]"
        :label="item[labelKey]"
        :value="item[valueKey]"
      />

      <!-- 分页控件 -->
      <div class="select-pagination">
        <el-pagination
          background
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          @current-change="handlePageChange"
          layout="prev, pager, next"
        />
      </div>
    </template>
  </el-select>
</template>

<script lang="ts" setup>
// 接收的props
const props = defineProps({
  // 绑定值（数组，因为是多选）
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 选项数据
  options: {
    type: Array,
    default: () => [],
  },
  // 每页显示数量
  pageSize: {
    type: Number,
    default: 10,
  },
  // value字段名
  valueKey: {
    type: String,
    default: 'id',
  },
  // label字段名
  labelKey: {
    type: String,
    default: 'label',
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择',
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否折叠标签
  collapseTags: {
    type: Boolean,
    default: true,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  // 最大选择数量
  multipleLimit: {
    type: Number,
    default: 0, // 0表示无限制
  },
})

// 发出的事件
const emit = defineEmits(['update:modelValue', 'page-change', 'change'])

// 当前选中值数组
const selectedValues = shallowRef([...props.modelValue])

// 监听选中值变化，同步到父组件
watch(
  () => selectedValues.value,
  (newVal) => {
    nextTick(() => {
      emit('update:modelValue', [...newVal])
    })
  },
)

// 监听父组件传入的modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (!arraysEqual(newVal, selectedValues.value)) {
      selectedValues.value = [...newVal]
    }
  },
)
// 辅助函数：判断两个数组是否相等
const arraysEqual = (arr1: any, arr2: any) => {
  if (arr1.length !== arr2.length) return false
  return arr1.every((value: any, index: string | number) => value === arr2[index])
}
// 分页相关
const currentPage = ref(1)
const total = ref(props.options.length)
const currentPageData = ref<any[]>([])

// 计算当前页数据
const calculateCurrentPageData = () => {
  const startIndex = (currentPage.value - 1) * props.pageSize
  const endIndex = startIndex + props.pageSize
  currentPageData.value = props.options.slice(startIndex, endIndex)
}

// 初始计算
calculateCurrentPageData()

// 监听选项数据变化
watch(
  () => props.options,
  (newOptions) => {
    total.value = newOptions.length
    // 数据变化时保持当前页，如果当前页超出总页数则重置到最后一页
    nextTick(() => {
      const totalPages = Math.ceil(total.value / props.pageSize) || 1
      if (currentPage.value > totalPages) {
        currentPage.value = totalPages
      }
      calculateCurrentPageData()
    })
  },
  { deep: true },
)

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  calculateCurrentPageData()
  emit('page-change', page) // 向外发送分页变化事件
}

// 处理下拉框显示/隐藏
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    // 显示时保持当前页，确保已选项在切换分页后仍能正确显示
    calculateCurrentPageData()
  }
}
</script>

<style lang="scss" scoped>
.select-pagination {
  display: flex;
  justify-content: center;
  padding: 8px;
  margin-top: 8px;
  border-top: 1px solid #eee;
}
</style>
