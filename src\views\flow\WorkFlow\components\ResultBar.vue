<script setup lang="ts">
import MarkdownIt from 'markdown-it'
// import <PERSON><PERSON><PERSON>ie<PERSON> from 'vue-json-viewer'
import useSSE from './useSSE'
import { postUpload } from '@/services/common'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  getData: {
    type: Function,
    default: () => {},
  },
})

interface ParamsItem {
  id: string
  name: string
  refType?: string
  dataType?: string
  value?: string
  required?: boolean
}

const params = ref<ParamsItem[]>([])
const refBox = ref()
const refForm = ref()
const drawer = ref(false)
const markdown = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true,
  typographer: true,
})

const fileValid = (item: any) => ({
  required: !!item.required,
  validator: (rule: any, value: any, callback: any) => {
    console.log(value, rule)
    if (!item.value && !!item.required) {
      callback(new Error('请上传解析文件'))
    } else {
      callback()
    }
  },
  trigger: 'change',
})

const { nodeList, connectSSE, handleStop, isLoading, pauseChatScroll } = useSSE(refBox)

const showInput = () => {
  const paramArr = props.getData()?.nodes
  console.log('paramArr', paramArr)
  for (let i = 0; i < paramArr.length; i++) {
    if (paramArr[i].type === 'startNode') {
      try {
        const list = JSON.parse(JSON.stringify(paramArr[i].data.parameters))
        list.forEach((item: any) => {
          if (item.defaultValue) {
            item.value = item.defaultValue
          } else {
            item.value = ''
          }
        })
        params.value = list
        break
      } catch (error) {
        params.value = []
      }
    }
  }
  console.log('params.value', params.value)
}

const getInputs = () => {
  const paramObj: any = {}
  try {
    params.value.map((item: any) => {
      const key = item.name
      const value = item.value
      paramObj[key] = value
    })
  } catch (error) {}
  return paramObj
}

const send = async () => {
  const promiseList: any[] = []
  refForm.value.forEach((form: any) => {
    const p = new Promise((resolve, reject) => {
      form.validate((valid: any) => {
        if (valid) {
          resolve(true)
        } else {
          reject('error send')
        }
      })
    })
    promiseList.push(p)
  })

  Promise.all(promiseList)
    .then(() => {
      nodeList.value = []
      const { id } = props
      const data = props.getData()
      const param = getInputs()
      const p = {
        id,
        param,
        data: JSON.stringify(data),
      }
      connectSSE(p)
    })
    .catch((err) => {
      console.log('error send!!', err)
      return false
    })
}

const handleEnter = (event: any, value: any) => {
  if (event.keyCode == 13) {
    if (!event.ctrlKey) {
      event.preventDefault()
      // 发送
      send()
    } else {
      value += '\n'
    }
  }
}

const getOutput = (summaryText: any) => {
  try {
    return markdown.render(exportCons(summaryText))
  } catch (error) {
    return ''
  }
}

const testRun = () => {
  showInput()
  drawer.value = true
}

const exportCons = (item: any) => {
  // return formatMD(item);
  return item
}

const handleClose = () => {
  drawer.value = false
}

const handleStopSSE = () => {
  handleStop()
}

defineExpose({
  testRun,
  exportCons,
})

const refUpload = ref()
const imagesList = {
  doc: 'doc',
  docx: 'docx',
  pdf: 'pdf',
  xlsx: 'xlsx',
  default: 'xlsx',
}

const maxFile = ref(100)
const imgUrl = ref(imagesList.default)
const loading = ref(false)

const List = computed(() => ['xlsx', 'xls', 'pdf', 'doc', 'docx'])
const acceptFileType = computed(() => List.value.map((f) => `.${f}`).join(','))

function uploadOriginFile(file: any, formIdx: any) {
  const fileContent = file.raw
  type FileExtension = keyof typeof imagesList
  let fileSuffix: FileExtension = fileContent.name.substring(fileContent.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase() as FileExtension
  if (!List.value.includes(fileSuffix)) {
    ElMessage.error('格式错误')
    return refUpload.value?.clearFiles()
  }
  if (fileContent.size >= maxFile.value * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${maxFile.value}M`)
    return refUpload.value.clearFiles()
  }

  imgUrl.value = imagesList[fileSuffix]
  loading.value = true
  const formData = new FormData()
  formData.append('file', fileContent)
  formData.append('suffix', 'html')
  getUpload(formData, formIdx)
}

async function getUpload(file: any, formIdx: any) {
  try {
    const { data = [] } = (await postUpload(file)) as { data: Array<{ templateUrl: string; templateName: string }> }
    const { templateUrl: contractUrl } = data[0]
    params.value[formIdx].value = contractUrl
    if (contractUrl) {
      refForm.value[formIdx].validate('file')
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <el-drawer v-model="drawer" :with-header="false" size="40%" @before-close="handleClose">
    <div ref="refBox" class="toolbar" @mousewheel="pauseChatScroll">
      <div class="box-inner">
        <div class="send-wrap">
          <el-button v-if="!isLoading" size="small" type="primary" @click="send">发送</el-button>
          <el-button v-else size="small" type="primary" @click="handleStopSSE">停止回答</el-button>
        </div>
        <div>
          <el-form
            v-for="(item, formIdx) in params"
            ref="refForm"
            :key="item.id"
            :model="item"
            label-position="top"
            @send.prevent
          >
            <el-form-item v-if="item.dataType === 'File'" prop="value" :label="item.name" :rules="fileValid(item)">
              <el-upload
                ref="refUpload"
                v-loading="false"
                class="upload-origin"
                action="#"
                drag
                :auto-upload="false"
                :show-file-list="false"
                :accept="acceptFileType"
                :on-change="(e) => uploadOriginFile(e, formIdx)"
              >
                <div v-if="!item.value" class="beforeUpload">
                  <i class="iconfont icon-nexus-upload"></i>
                  <div class="txt-tips">仅支持解析 {{ acceptFileType }} 文档，并批量生成规则</div>
                </div>
                <div v-else class="reupload-box">
                  <!-- <img class="icon-upload" :src="imgUrl" alt="" /> -->
                  <SvgIcons :name="imgUrl" width="38px" height="38px" />
                  <div class="file-box">
                    <div class="filename">{{ item.value }}</div>
                    <svg-icon icon-class="reupload" class="reupload" />重新上传
                  </div>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item
              v-else-if="item.dataType === 'Array'"
              prop="value"
              :label="item.name"
              :rules="{ required: item.required, message: '请输入内容', trigger: 'change' }"
            >
              <el-select v-model="item.value" multiple filterable allow-create placeholder="请选择"> </el-select>
            </el-form-item>
            <el-form-item
              v-else
              prop="value"
              :label="item.name"
              :rules="{ required: item.required, message: '请输入内容', trigger: 'blur' }"
            >
              <el-input
                ref="refInput"
                v-model="item.value"
                :disabled="isLoading"
                class="textarea-box"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 5 }"
                :placeholder="isLoading ? '发送中...' : `请输入内容${item.required ? '(必填)' : ''}`"
                @keydown="(e) => handleEnter(e, item.value)"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="cards-wrapper">
          <el-card
            v-for="(v, idx) in nodeList"
            :key="idx"
            class="card markdown-body"
            :style="{ '--color': '#5cb87a' }"
            shadow="always"
          >
            <template #header>
              <div class="card-header">
                <span>{{ v.nodeName }}</span>
              </div>
            </template>
            <!-- <div>{{ nodeList }}</div> -->
            <!-- 卡片重写 -->
            <!-- <vue-markdown v-if="v.type === 'flux'" :html="true" :source="exportCons(v.data)"></vue-markdown>-->
            <div v-if="v.type === 'flux'" ref="refAnswer" class="chat-answer markdown-body">
              <div
                v-if="v.reasoningContent"
                class="content-container deepThinkContent"
                v-html="getOutput(v.reasoningContent)"
              ></div>
              <div v-if="v.content" class="content-container answerContent" v-html="getOutput(v.content)"></div>
            </div>
            <!-- <JsonViewer v-else :value="v.data" :expand-depth="0"></JsonViewer> -->
            <!-- {{ v.data }} -->
            <!-- <div v-else>{{ JSON.stringify(v.data, null, 4) }}</div> -->
            <el-input
              v-else
              :value="JSON.stringify(v.data, null, 4)"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 5 }"
              :disabled="true"
            ></el-input>
          </el-card>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style lang="scss">
ol {
  padding-inline-start: 1.5rem;
  margin-left: 0.75rem;
  list-style-type: decimal;
}
ul {
  padding-inline-start: 1.5rem;
  list-style-type: disc;
}
</style>
<style lang="scss" scoped>
.toolbar {
  width: 100%;
  height: 100%;
  padding: 10px;
  overflow: hidden auto;
  pre {
    white-space: pre-wrap;
    cursor: pointer;
  }
  .send-wrap {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
.cards-wrapper {
  height: 100%;
  .chat-answer {
    .content-container {
      padding: 0.25rem;
      border: 1px solid #eaeaea;
      border-radius: 0.25rem;
    }
    .deepThinkContent {
      margin-bottom: 0.5rem;
    }
  }
}
.card {
  display: inline-block;
  width: 100%;
  min-height: 108px;
  margin-top: 12px;
  margin-bottom: 0;
  word-wrap: break-word;
  background: #fff;
}
:deep(.el-card__body) {
  border-top: 5px solid var(--color);
}
:deep(.el-textarea.is-disabled .el-textarea__inner) {
  color: #606266;
  cursor: auto;
  background-color: #fff;
}
.upload-origin {
  width: 100%;
  height: 66px;
  :deep(.el-upload--text) {
    width: 100%;
    height: 100%;
  }
  :deep(.el-upload-dragger) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    &:hover {
      background-color: #492ed112;
    }
    .reupload-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      color: var(--main-bg);
      .icon-upload {
        display: block;
        width: 30px;
        height: 30px;
      }
      .file-box {
        display: flex;
        align-items: center;
        .filename {
          max-width: 200px;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
          color: #262626;
          white-space: nowrap;
        }
      }
    }
  }
  .iconfont {
    font-size: 24px;
    color: #a2a1a9;
  }
  .txt-tips {
    font-size: 12px;
    line-height: 18px;
    color: #929292;
    text-align: center;
  }
}
</style>
