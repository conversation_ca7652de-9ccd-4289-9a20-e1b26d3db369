import { useAppStore } from '@/stores/app'
import { requesetLogin } from '@/services/login' // getUserInfo
import type { ILoginParams } from '@/services/login'
// import { delCookie } from '@/utils'

export default function initLogin() {
  const router = useRouter()
  const appStore = useAppStore()
  const loginData = ref<ILoginParams>({ userName: 'admin', password: 'Fadada123' })
  const loginLoading = ref(false)

  async function submitForm() {
    ///////// 登录接口参数&参数类型待补充
    const params: ILoginParams = {
      userName: loginData.value.userName,
      password: loginData.value.password,
    }
    loginLoading.value = true
    try {
      const { data } = await requesetLogin(params)

      appStore.setToken(data as string)
      router.push({ name: 'Checklist' })

      ElMessage({
        type: 'success',
        message: '登录成功',
      })
    } catch (error) {
      console.log('error', error)
    } finally {
      loginLoading.value = false
    }
  }

  return {
    loginData,
    loginLoading,
    submitForm,
  }
}
