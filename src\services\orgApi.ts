import request from '@/services'
import type { IResponse } from '@/services'



// 合同上传-doc (组织相关)
export function postOrgUpload(data: any): Promise<IResponse<any[]>> {
  return request({
    url: `/attachment/upload-temporary`,
    method: 'post',
    data,
  })
}

/**
 * 新增组织
 * @param { *orgName *rootFlag *parentId }
 * @returns
 */
export function addOrgRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/save-org`,
    method: 'post',
    data,
  })
}

/**
 * 编辑组织
 * @param { *orgId *orgName *parentId }
 * @returns
 */
export function editOrgRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/update-org`,
    method: 'post',
    data,
  })
}

/**
 * 删除组织
 * @param { *orgIdList[array] }
 * @returns
 */
export function deleteOrgRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/delete-org`,
    method: 'post',
    data,
  })
}

/**
 * 拖拽组织节点
 * @param { *sourceOrgId(操作的组织id) *targetOrgId(被操作的组织id) }
 * @param { *moveFlag [ up-将sourceOrgId移动（拖动）到targetOrgId上方, inner-将sourceOrgId移动（拖动）到targetOrgId里面, down-将sourceOrgId移动（拖动）到targetOrgId下方] }
 */
export function dragOrgRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/drag-org`,
    method: 'post',
    data,
  })
}

/**
 * 上下移动组织
 * @param {*} data
 * @returns
 */
export function moveOrgRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/move-org`,
    method: 'post',
    data,
  })
}

/**
 *  通过机构id获取用户
 *  @param { *orgId:组织id, subFlag:是否查询子级, searchContent:搜索内容 }
 */
export function getUserByOrgId(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/get-user-list-by-orgId`,
    method: 'post',
    data,
  })
}

/**
 *  分页获取组织信息
 *  @param { searchContent, page, pageSize }
 */
export function queryOrgPageList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/get-org-list`,
    method: 'post',
    data,
  })
}

// 新增用户
export function addUserRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/save-user`,
    method: 'post',
    data,
  })
}

// 编辑用户
export function editUserRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/update-user`,
    method: 'post',
    data,
  })
}

// 批量导入用户
export function batchImportUser(data: any, fn?: (progress: number) => void): Promise<IResponse<any>> {
  return request({
    url: `/org/user/import-batch-user`,
    method: 'post',
    data,
    onUploadProgress: function (e: any) {
      fn && fn(Math.round((e.loaded * 100) / e.total))
    },
  })
}

// 通过userCode获取用户信息
export function queryUserInfoByCode(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/get-user-info-by-user-code`,
    method: 'post',
    data,
  })
}

// 获取接收人明细列表
export function queryTransferorList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/get-transferor-list-by-user-code`,
    method: 'post',
    data,
  })
}

// 转交他人
export function dataTransfer(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/data-transfer`,
    method: 'post',
    data,
  })
}

// 下载用户导入模板
export function templateDown(data: any): Promise<any> {
  return request({
    url: `/合同产品-用户导入模板.xlsx`,
    method: 'GET',
    responseType: 'blob',
    params: data,
  })
}

/**
 * 下载错误信息
 * @param {*} checkKey
 * @returns
 */
export function downloadErrorInfo(params: any): Promise<any> {
  return request({
    url: `/org/user/export-org-user-error`,
    method: 'get',
    params,
    responseType: 'blob',
  })
}

// 删除用户
export function deleteUserRequest(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/delete-user`,
    method: 'post',
    data,
  })
}

// 获取用户的权限数据列表
export function queryAuthDataByUsercode(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/get-auth-data-by-user-code`,
    method: 'post',
    data,
  })
}

/**
 * 移动人员
 * @param {*currentOrgId *transferOrgIdList *userCodeList}
 * @returns
 */
export function moveOrgUser(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/remove-user-by-orgId`,
    method: 'post',
    data,
  })
}

/**
 *  查询组织内的所有用户
 */
export function queryAllOrgUser(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/get-all-user-list-by-root-org`,
    method: 'post',
    data,
  })
}

// 设置部门负责人
export function setLeaderReq(data: any): Promise<IResponse<any>> {
  return request({
    url: `/v1/org/user/set-org-leader`,
    method: 'post',
    data,
  })
}

// 取消部门负责人
export function delOrgLeader(data: any): Promise<IResponse<any>> {
  return request({
    url: `/org/user/v1/delete-org-leader`,
    method: 'post',
    data,
  })
}
