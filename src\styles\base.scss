* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --font-color: 0, 0, 0;
  --bg-color: rgba(#fff);
  --main-bg: #773bef;
  --main-font: #492ed1;
  --minor-font: #221d39;
  --sidebar-bg: #faf9fb;
  --sidebar-line: #a2a1a9;
  --header-bottom-line: #f0f2f6;
  --primary-btn-bg: #492ed112;
  --menu-bg-active: #773bef1a;
  --page-header-line: #eaebf0;
  --input-bg: #f2f3f6;
  --popper-font: #464359;
  --is-color-773bef: #773bef;
  --is-color-492ed1: #492ed1;
  --is-color-773bef1a: #773bef1a;
  --is-color-492ed112: #492ed112;
  --is-color-6d58da: #6d58da;
  --is-color-3a25a7: #3a25a7;
  --is-color-e8c46b: #e8c46b;
  --is-color-faf9fb: #faf9fb;
  --is-color-221d39: #221d39;
  --is-color-464359: #464359;
  --is-color-7d7b89: #7d7b89;
  --is-color-a2a1a9: #a2a1a9;
  --is-color-eaebf0: #eaebf0;
  --is-color-fafafb: #fafafb;
  --is-color-d0d1dc: #d0d1dc;
  --is-color-ffffff: #fff;
  --is-color-1b9275: #1b9275;
  --is-color-e0972e: #e0972e;
  --is-color-e6555e: #e6555e;
  --is-color-ea767d: #ea767d;
  --is-color-bf4a57: #bf4a57;
  --is-color-f2f3f6: #f2f3f6;
}
:root.dark {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}
body {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  color: var(--font-color);
  background-color: var(--bg-color);
}
::view-transition-new(root),
::view-transition-old(root) {
  /* 关闭默认动画 */
  animation: none;
}
html {
  font-size: 16px;
}

@media screen and (width <= 768px) {
  html {
    font-size: 14px; /* 移动端字体大小 */
  }
}
