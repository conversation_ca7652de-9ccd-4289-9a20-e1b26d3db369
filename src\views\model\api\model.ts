import request from '@/services'
import type { IResponse } from '@/services'

// 获取模型列表参数
// 请求参数
export interface IModelQueryData {
  modelName?: string // 模型名称
  modelType?: number // 模型类型：0-文本生成 1-文本嵌入模型 2-视觉嵌入模型 ...
  remark?: string
}
interface IQueryPageModelListParams {
  pageNum: number
  pageSize: number
  data?: IModelQueryData
}
// 响应参数
export interface IModel {
  id: string
  modelName: string // 模型名称
  baseUrl: string // 模型请求地址
  apiKeyValue: string // 模型密钥
  modelType: number // 模型类型
  modelTypeName: string // 模型类型名称
  remark: string // 备注
  enableStatus: boolean // 是否启用
  createBy: string
  createByName: string
  createTime: string
  updateBy: string
  updateByName: string
  updateTime: string
  corpId?: string // 租户
}
export interface IQueryPageModelList {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: IModel[]
}
// 获取模型列表接口
export function queryPageModelList<T>(data: IQueryPageModelListParams): Promise<IResponse<T>> {
  return request({
    url: `/model/page`,
    method: 'post',
    data,
  })
}

// 启用模型
export function enableModel<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/model/enable/${id}`,
    method: 'post',
  })
}

// 禁用模型
export function disableModel<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/model/disable/${id}`,
    method: 'post',
  })
}

// 获取模型分类列表响应参数
export interface IModelType {
  value: number
  modelTag: string
}
// 获取模型分类列表接口
export function queryModelTypeList<T>(): Promise<IResponse<T>> {
  return request({
    url: `/model/category`,
    method: 'get',
  })
}

// 保存AI模型基本配置
export interface ISaveModelInfoParams {
  modelName: string
  modelType: number
  baseUrl: string
  apiKey: string
  remark: string
}
export function saveModelInfo<T>(data: ISaveModelInfoParams): Promise<IResponse<T>> {
  return request({
    url: `/model/save`,
    method: 'post',
    data,
  })
}

// 修改AI模型基本配置
export interface IEditModelInfoParams extends ISaveModelInfoParams {
  id: string
}
export function editModelInfo<T>(data: IEditModelInfoParams): Promise<IResponse<T>> {
  return request({
    url: `/model/update`,
    method: 'post',
    data,
  })
}

// 响应参数 IModel 已经在上面定义了，不需要重复定义
// 根据 id 获取模型详情
export function queryModelDetail<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/model/detail/${id}`,
    method: 'get',
  })
}

// 删除AI模型配置表
export function deleteModel<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/model/remove/${id}`,
    method: 'delete',
  })
}
