import { delLawCaseById, getLawCaseList, updateLawCase, updateShareScope } from '@/services/judicialCase'
import type { ILawCase, ILawCaseSearchArgs, IParams, IResponse } from '../types'
import type { LawCase } from './Model'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

export function useCaseService() {
  const loading = ref(false)
  const pageNum = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const tableData = ref<LawCase[]>([])

  const queryLawCaseList = async (args?: ILawCaseSearchArgs) => {
    loading.value = true
    if (args?.judge && Array.isArray(args.judge)) {
      args.judgeStartDate = args.judge[0]
      args.judgeEndDate = args.judge[1]
    }
    const queryParams = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      data: {
        ...args,
      },
    }

    try {
      const res = await getLawCaseList<IResponse<ILawCase>, IParams<ILawCaseSearchArgs>>(queryParams)

      const { data } = res
      tableData.value = data.list
      total.value = Number(data.total)
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const handleSizeChange = (val: number) => {
    pageSize.value = val
    pageNum.value = 1
    queryLawCaseList()
    console.log('2')
  }

  const handleCurrentChange = (val: number) => {
    pageNum.value = val
    console.log('1')
    queryLawCaseList()
  }

  const handleDel = (id: string) => {
    ElMessageBox.confirm('确认删除这条记录吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      showClose: false,
    })
      .then(async () => {
        console.log(id)
        const { code, message } = await delLawCaseById(id)
        if (code === RESPONSE_CODE_SUCCESS) {
          queryLawCaseList()
          ElMessage.success('删除成功')
        } else {
          ElMessage.error(message)
        }
      })
      .catch(() => {
        console.log('取消删除比对记录')
      })
  }
  const search = (args?: ILawCaseSearchArgs) => {
    pageNum.value = 1
    queryLawCaseList(args)
  }

  const update = async (params: any) => {
    const { code, message } = await updateLawCase(params)
    if (code === RESPONSE_CODE_SUCCESS) {
      search()
      ElMessage.success('更新成功')
    } else {
      ElMessage.error(message)
    }
  }
  const updateScope = async (params: any) => {
    const { code, message } = await updateShareScope(params)
    if (code === RESPONSE_CODE_SUCCESS) {
      search()
      ElMessage.success('更新成功')
    } else {
      ElMessage.error(message)
    }
  }
  return {
    tableData,
    total,
    pageSize,
    pageNum,
    loading,
    updateScope,
    queryLawCaseList,
    search,
    handleDel,
    update,
    handleCurrentChange,
    handleSizeChange,
  }
}
