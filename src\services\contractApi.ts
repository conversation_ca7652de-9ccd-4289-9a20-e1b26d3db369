import request from '@/services'
import type { IResponse } from '@/services'

// 合同类型数据接口
export interface ContractTypeItem {
  id?: string
  contractTypeName?: string
  parentId?: string
  contractTypeStatus?: boolean | number
  children?: ContractTypeItem[]
  level?: number
}


/**
 * 合同类型列表查询（无分页）
 */
export function getContractTypeList(data: any): Promise<IResponse<ContractTypeItem[]>> {
  return request({
    url: '/contract-type/list',
    method: 'post',
    data
  })
}

/**
 * 新增合同类型
 */
export function addContractType(data: ContractTypeItem): Promise<IResponse<any>> {
  return request({
    url: '/contract-type/add',
    method: 'post',
    data,
  })
}

/**
 * 删除合同类型
 */
export function deleteContractType(data: ContractTypeItem): Promise<IResponse<any>> {
  return request({
    url: '/contract-type//delete',
    method: 'post',
    data,
  })
}

/**
 * 编辑合同类型
 */
export function editContractType(data: ContractTypeItem): Promise<IResponse<any>> {
  return request({
    url: '/contract-type//edit',
    method: 'post',
    data,
  })
}

/**
 * 更新合同类型状态
 */
export function updateContractTypeStatus(data: ContractTypeItem): Promise<IResponse<any>> {
  return request({
    url: '/contract-type//updateStatus',
    method: 'post',
    data,
  })
}

