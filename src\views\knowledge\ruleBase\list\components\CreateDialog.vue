<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    title="AI解析审查清单"
    append-to-body
    :close-on-click-modal="false"
    width="500px"
    show-close
    @close="close"
  >
    <el-form ref="refForm" label-width="110px" :model="ruleForm" :rules="rules" label-position="top">
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="ruleForm.fileType" placeholder="请选择文件类型" style="width: 100%">
          <el-option v-for="item in fileTypeOptionsList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="清单名称" prop="name">
        <el-input v-model="ruleForm.name" show-word-limit maxlength="50" clearable placeholder="请输入清单名称" />
      </el-form-item>
      <el-form-item v-if="ruleForm.fileType === '2' || ruleForm.fileType === '4'" label="审查立场" prop="standpoint">
        <el-input v-model="ruleForm.standpoint" show-word-limit maxlength="30" clearable placeholder="请输入审查立场" />
      </el-form-item>
      <el-form-item label="适用合同类型" prop="contractTypes">
        <el-tree-select
          v-model="ruleForm.contractTypes"
          :data="props.contractTypeTreeData"
          :props="{ label: 'contractTypeName', value: 'id', children: 'children' }"
          placeholder="请选择适用合同类型"
          style="width: 100%"
          multiple
          show-checkbox
          check-strictly
          collapse-tags
          collapse-tags-tooltip
          filterable
        />
      </el-form-item>
      <el-form-item label="上传解析文件" prop="file">
        <el-upload
          ref="refUpload"
          v-loading="loading"
          class="upload-origin"
          action="#"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :accept="acceptFileType"
          :on-change="uploadOriginFile"
        >
          <div v-if="!fileInfo.contractName" class="beforeUpload">
            <i class="iconfont icon-pro-upload"></i>
            <div class="txt-tips">点击或拖拽文件到此处上传</div>
            <div class="txt-tips">仅支持解析 {{ acceptFileType }} 文档，并批量生成规则</div>
            <div class="txt-tips">文件大小不能超过{{ maxFile }}M</div>
          </div>
          <div v-else class="reupload-box">
            <!-- <img class="icon-upload" :src="imgUrl" alt="" /> -->
            <SvgIcons :name="imgUrl" width="38px" height="38px" />
            <div class="file-box">
              <div class="filename">{{ fileInfo.contractName }}</div>
              <SvgIcons name="reupload" class="reupload" />重新上传
            </div>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="ruleForm.remark"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :maxlength="200"
          show-word-limit
          clearable
          placeholder="请输入备注"
        />
      </el-form-item>
      <div class="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleCommit">确定</el-button>
      </div>
    </el-form>
    <SelectUserDialog ref="refUserDialog" />
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { getAIGenerate, postUpload } from '@/services/ruleBaseApi'
// import { authTypeList } from '../const'
import SelectUserDialog from './SelectUserDialog.vue'
import { ElMessage } from 'element-plus'

// Props 定义
const props = defineProps<{
  contractTypeTreeData: any[]
}>()

// 定义 emits
const emit = defineEmits<{
  refresh: []
}>()

// 定义 props (如果需要的话)
// interface Props {
//   // 在这里定义 props 类型
// }

// const props = withDefaults(defineProps<Props>(), {
//   // 默认值
// })

const rules = reactive({
  name: [
    { required: true, message: '请输入清单名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度限制在2-50个字', trigger: 'blur' },
  ],
  standpoint: [
    { required: true, message: '请输入审查立场', trigger: 'blur' },
    { min: 2, max: 30, message: '长度限制在2-30个字', trigger: 'blur' },
  ],
  fileType: [{ required: true, message: '请选择文件类型', trigger: 'blur' }],
  contractTypes: [{ required: true, message: '请选择适用合同类型', trigger: 'change' }],
  globallyVisibleFlag: [{ required: true, message: '请选择授权类型', trigger: 'change' }],
  visibleUsers: [{ required: true, message: '请选择可见范围', trigger: 'change' }],
  file: [
    {
      required: true,
      validator: (_rule: any, _value: any, callback: any) => {
        if (!fileInfo.value.contractName) {
          callback(new Error('请上传解析文件'))
        } else {
          callback()
        }
      },
      trigger: 'change', // 触发时机还有哪些值
    },
  ],
})

const visible = ref(false)
const refUserDialog = ref<any>(null)
// 定义用户类型接口
interface User {
  userCode: string
  realName: string
}

// 记录临时选中的可见范围用户
const tempUserList = ref<User[]>([])

const iniData = {
  name: '',
  standpoint: '',
  fileType: '2',
  background: '',
  globallyVisibleFlag: true,
  visibleUsers: [],
  contractTypes: [],
  remark: '',
}
const ruleForm = ref(iniData)

// 监听 fileType 变化，清除已上传文件
watch(
  () => ruleForm.value.fileType,
  () => {
    if (refUpload.value) {
      refUpload.value.clearFiles()
    }
    fileInfo.value = {}
  },
)

const imagesList: Record<string, string> = {
  doc: 'doc',
  docx: 'docx',
  pdf: 'pdf',
  xlsx: 'xlsx',
  default: 'xlsx',
}

const List = computed(() => (ruleForm.value.fileType === '2' ? ['xlsx', 'xls'] : ['pdf', 'doc', 'docx']))
const acceptFileType = computed(() => List.value.map((f) => `.${f}`).join(','))
console.log('acceptFileType', acceptFileType.value)

const fileTypeOptionsList = [
  { name: '合同文件解析', value: '4' },
  { name: '规则文件解析', value: '2' },
  { name: '政策文件解析', value: '3' },
]
const maxFile = ref(20)
const refUpload = ref<any>(null)
const isDocFile = ref(false)
const loading = ref(false)
const imgUrl = ref(imagesList.default)
const fileInfo = ref<{ contractName?: string; contractUrl?: string }>({})

// 移除本地合同类型数据定义，改为使用 props

async function open() {
  visible.value = true
}
function close() {
  refForm.value?.resetFields()
  visible.value = false
  fileInfo.value = {}
}

function uploadOriginFile(file: any) {
  const fileContent = file.raw
  let fileSuffix = fileContent.name.substring(fileContent.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase()
  if (!List.value.includes(fileSuffix)) {
    ElMessage.error('格式错误')
    return refUpload.value.clearFiles()
  }
  if (fileContent.size >= maxFile.value * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${maxFile.value}M`)
    return refUpload.value.clearFiles()
  }

  imgUrl.value = imagesList[fileSuffix]
  isDocFile.value = ['docx', 'doc'].includes(fileSuffix)
  loading.value = true
  const formData = new FormData()
  formData.append('file', fileContent)
  formData.append('suffix', 'html')
  getUpload(formData, isDocFile)
}

async function getUpload(file: any, _isDocFile: any) {
  try {
    const { data = [] } = await postUpload(file)
    const { templateUrl: contractUrl, templateName: contractName } = data[0]
    fileInfo.value = { contractName, contractUrl }
    refForm.value?.validateField('file')
  } catch (error) {
    console.error('Upload error:', error)
  } finally {
    loading.value = false
  }
}

const refForm = ref<any>(null)
function handleCommit() {
  refForm.value?.validate(async (valid: any, fields: any) => {
    if (valid) {
      const params = {
        ruleListName: ruleForm.value.name,
        rulePosition: ruleForm.value.standpoint,
        sourceType: ruleForm.value.fileType,
        fileCode: fileInfo.value.contractUrl,
        reviewBackground: ruleForm.value.background || undefined,
        globallyVisibleFlag: ruleForm.value.globallyVisibleFlag,
        visibleUsers: ruleForm.value.visibleUsers,
      }
      await getAIGenerate(params)
      ElMessage.success('创建成功！')
      emit('refresh')
      close()
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 合同类型数据已在父组件挂载时加载

// 暴露给模板的方法
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
:deep(.hide-select-option-popper) {
  display: none;
}
:deep(.el-dialog__body) {
  padding-bottom: 12px;
}
.dialog-wrap {
  .upload-origin {
    width: 100%;
    height: 146px;
    :deep(.el-upload--text) {
      width: 100%;
      height: 100%;
    }
    :deep(.el-upload-dragger) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      &:hover {
        background-color: #492ed112;
      }
      .reupload-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
        color: var(--main-bg);
        .icon-upload {
          display: block;
          width: 60px;
          height: 60px;
        }
        .file-box {
          display: flex;
          align-items: center;
          .filename {
            max-width: 150px;
            margin-right: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            color: #262626;
            white-space: nowrap;
          }
        }
      }
    }
    .iconfont {
      font-size: 24px;
      color: #a2a1a9;
    }
    .txt-tips {
      font-size: 12px;
      line-height: 18px;
      color: #929292;
      text-align: center;
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
