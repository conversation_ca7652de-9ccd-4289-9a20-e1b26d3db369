// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-nocheck
import { BASE_API } from '@/utils/config'
// import { getToken } from '@/utils/token' //////// 暂无
// import { tokenKey } from '@/utils/config' //////// 暂无
import { fetchEventSource } from '@microsoft/fetch-event-source'
// import useScroll from './useScroll';
import useChatScroll from '@/composables/useChatScroll'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/stores/app'
export default function useSSE(refBox) {
  let controller
  const isStartSSE = ref(false)
  const nodeMap = reactive({ map: new Map() })
  const nodeList: any = ref([])
  const isLoading = ref(false)
  // const { scrollToBottom, chatScrollFlag, handleChatScroll } = useScroll(refBox);
  const { scrollToBottom, pauseChatScroll } = useChatScroll(refBox)
  function connectSSE(params) {
    isStartSSE.value = true
    controller = new AbortController()
    const signal = controller.signal
    isLoading.value = true
    const originNodes = JSON.parse(params.data)
    nodeList.value = []
    nodeMap.map.clear() // 清空之前的节点数据
    const appStore = useAppStore()
    fetchEventSource(`${BASE_API}/workflow/exe-stream`, {
      method: 'POST',
      signal: signal,
      headers: {
        'Content-Type': 'application/json',
        ['csrf_token']: appStore.token,
      },
      body: JSON.stringify(params),
      async onopen(response) {
        if (response.ok && response.headers.get('content-type') === 'text/event-stream') {
          // speedMessage();
          return
        } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
          if (response.status == 401) {
            ElMessage.error('您的会话已过期，请重新登录')
            // root.$store.dispatch('user/resetState').then(() => {
            //   location.reload()
            // })
          } else {
            ElMessage.error('服务错误')
          }
          handleErrorSSE()
        } else {
          ElMessage.error('服务错误')
          handleErrorSSE()
        }
      },
      onmessage(msg) {
        try {
          const result = JSON.parse(msg.data)

          // 根据result.nodeId存储到nodeMap中
          if (result.nodeId) {
            // 如果nodeMap中已存在该节点，更新数据，否则创建新节点
            if (nodeMap.map.has(result.nodeId)) {
              const existingNode = nodeMap.map.get(result.nodeId)

              // 更新content
              if (result.type === 'flux') {
                existingNode.content += result.content ? result.content : ''
              } else if (result.type === 'json' && result.content) {
                existingNode.content = result.content
              }

              // 更新reasoningContent
              if (result.reasoningContent) {
                existingNode.reasoningContent = existingNode.reasoningContent + result.reasoningContent
              }
            } else {
              // 创建新节点
              nodeMap.map.set(result.nodeId, {
                nodeId: result.nodeId,
                nodeName: result.nodeName || '',
                data: result.data || {},
                content: result.content || '',
                reasoningContent: result.reasoningContent || '',
                show: true,
                type: result.type || '',
              })
            }
          }
          nodeList.value = Array.from(nodeMap.map.values())

          scrollToBottom()
        } catch (error) {
        } finally {
          isStartSSE.value = false
          isLoading.value = false
        }
      },
      onerror(err) {
        console.log(err)
        isStartSSE.value = false
        isLoading.value = false
        handleErrorSSE(err)
      },
      onclose() {
        isStartSSE.value = false
        isLoading.value = false
        controller.abort()
        return
      },
      openWhenHidden: true,
    })
  }

  function handleStop() {
    isStartSSE.value = false
    isLoading.value = false
    controller?.abort()
  }

  function handleErrorSSE() {
    throw new Error()
  }

  function transNodes(list: any[]) {
    const _list = list.map((item) => ({
      nodeId: item.id,
      content: '',
      show: false,
      originType: item.type,
    }))
    const index = _list.findIndex((item) => item.originType === 'endNode')
    if (index !== -1) {
      _list.push(_list.splice(index, 1)[0])
    }
    return _list
  }

  return {
    connectSSE,
    nodeList,
    isStartSSE,
    handleStop,
    isLoading,
    pauseChatScroll,
  }
}
