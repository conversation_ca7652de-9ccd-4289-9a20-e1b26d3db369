<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TableLayout from '@/components/TableLayout/index.vue'
import type { TableColumn } from '@/components/TableLayout/types'
import SearchBox from './components/SearchBox.vue'
import CreateDialog from './components/CreateDialog.vue'
import { getLabelPage, changeLabelStatus, type LabelItem } from '@/services/labelApi'

// 表格引用
const tableLayoutRef = ref()

// 搜索参数
const searchParams = ref({})

// 表格列配置
const columns: TableColumn[] = [
  {
    prop: 'labelName',
    label: '标签名称',
  },
  {
    prop: 'bizTypeName',
    label: '业务类型',
  },
  {
    prop: 'enableStatus',
    label: '状态',
    width: 300,
    slot: 'statusColumn',
  },
]

// 搜索参数处理
const handleSearchParams = async (params: Record<string, any>) => {
  searchParams.value = params
  await nextTick()
  if (tableLayoutRef.value) {
    tableLayoutRef.value.clearSelection()
    tableLayoutRef.value.refresh()
  }
}

// 数据加载完成处理
const handleDataLoaded = (data: LabelItem[]) => {
  console.log('标签数据加载完成:', data)
}

// 刷新表格
const refreshTable = () => {
  if (tableLayoutRef.value) {
    tableLayoutRef.value.clearSelection()
    tableLayoutRef.value.refresh()
  }
}

// 简化版本：直接返回箭头函数
const beforeChange = (row: LabelItem) => async () => {
  const newStatus = !row.enableStatus

  // 禁用时确认
  if (!newStatus) {
    try {
      await ElMessageBox.confirm('是否禁用该标签？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
    } catch {
      return false
    }
  }

  try {
    await changeLabelStatus({ id: row.id, enableStatus: newStatus })
    ElMessage.success('操作成功')
    return true
  } catch {
    ElMessage.error('操作失败')
    return false
  }
}

// 弹窗引用
const refCreate = ref<any>(null)

// 打开弹窗
const handleOpenDialog = (row?: LabelItem) => {
  console.log('处理打开弹窗:', row)
  if (row) {
    // 编辑模式 - 调用 CreateDialog 并传递编辑数据
    refCreate.value?.open(row)
  } else {
    // 新增模式 - 调用 CreateDialog
    refCreate.value?.open()
  }
}

// 新增标签
const handleAdd = () => {
  refCreate.value?.open()
}
</script>
<template>
  <SearchBox @search="handleSearchParams" />
  <div style="padding: 16px; text-align: right">
    <el-button type="primary" @click="handleAdd">新增标签</el-button>
  </div>
  <TableLayout
    ref="tableLayoutRef"
    :api-function="getLabelPage"
    :api-params="searchParams"
    :columns="columns"
    @data-loaded="handleDataLoaded"
    :heightOffset="185"
  >
    <!-- 状态列插槽 -->
    <template #statusColumn="{ row }">
      <el-switch v-model="row.enableStatus" :before-change="beforeChange(row)" />
    </template>

    <!-- 操作列插槽 -->
    <template #actions="{ row }">
      <div class="action-buttons">
        <el-button type="text" @click="handleOpenDialog(row)">编辑</el-button>
      </div>
    </template>
  </TableLayout>

  <!-- 弹窗组件 -->
  <CreateDialog ref="refCreate" @refresh="refreshTable" />
</template>
<style lang="scss" scoped>
.go-detail {
  color: var(--main-bg);
  cursor: pointer;
}
.status-dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-right: 5px;
  margin-bottom: 2px;
  border-radius: 50%;
}
.visibility-toggle {
  color: var(--main-bg);
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.warning-text {
  color: #e6555e !important;
}
</style>
