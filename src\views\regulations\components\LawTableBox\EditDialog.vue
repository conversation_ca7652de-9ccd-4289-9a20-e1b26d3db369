<script lang="ts" setup>
import type { FormInstance } from 'element-plus'

const visible = ref(false)
let aResolve: (value: unknown) => void

function open() {
  visible.value = true
  return new Promise((resolve) => {
    aResolve = resolve
  })
}

function close(flag?: boolean) {
  visible.value = false
  aResolve(!!flag)
}

const refForm = ref<FormInstance>()

const formData = ref({
  lawName: '',
  book: '',
  chapter: '',
  festival: '',
  terms: '',
  tag: '',
})

function handleCommit() {
  close(true)
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="编辑" v-model="visible" :close-on-click-modal="false" width="500px">
      <el-form ref="refForm" label-width="110px" :model="formData">
        <el-form-item label="法律名称" prop="lawName">
          <el-input v-model="formData.lawName" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="篇" prop="book">
          <el-input v-model="formData.book" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="章" prop="chapter">
          <el-input v-model="formData.chapter" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="节" prop="festival">
          <el-input v-model="formData.festival" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="条款" prop="terms">
          <el-input v-model="formData.terms" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-button>+添加标签</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
