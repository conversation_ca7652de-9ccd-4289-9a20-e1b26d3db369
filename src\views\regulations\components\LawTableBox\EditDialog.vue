<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import { useGeneralService } from '@/composables/useGeneralService'
import { getLawInfoById, updateLawInfoDetail } from '@/services/regulations'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
const { tagsData, queryTagsList, TagBizType } = useGeneralService()
const visible = ref(false)
const emit = defineEmits(['close'])
async function open(law: any) {
  console.log(law)
  refForm.value?.clearValidate()
  refForm.value?.resetFields()
  visible.value = true
  await queryTagsList(String(TagBizType.LAW_CLAUSE))
  await loadDetail(law.id)
  formData.value.lawName = law.lawName
  formData.value.id = law.id
}

const loadDetail = async (id: string) => {
  const { code, data, message } = await getLawInfoById(id)
  if (code === RESPONSE_CODE_SUCCESS) {
    formData.value = data as any
    formData.value.labelIds = (data as any).labels.map((item: any) => item.id)
  } else {
    ElMessage.error(message)
  }
}

function close() {
  visible.value = false
}
const rules = {
  provisionsContent: [{ required: true, message: '请输入条款具体内容', trigger: 'change' }],
}
const refForm = ref<FormInstance>()

const formData = ref({
  id: '',
  lawName: '',
  topicContent: '',
  chapterContent: '',
  jointContent: '',
  provisionsContent: '',
  labelIds: [] as string[],
})

const update = async (postData: any) => {
  const { code, message } = await updateLawInfoDetail(postData)
  if (code === RESPONSE_CODE_SUCCESS) {
    ElMessage.success(message)
    emit('close')
    close()
  } else {
    ElMessage.error(message)
  }
}
function handleCommit() {
  refForm.value?.validate((isValid: boolean) => {
    if (isValid) {
      update(formData.value)
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="编辑" v-model="visible" :close-on-click-modal="false" width="70vw">
      <el-form ref="refForm" label-width="110px" :model="formData" :rules="rules">
        <el-form-item label="法律名称" prop="lawName">
          <el-input v-model="formData.lawName" disabled placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="篇" prop="topicContent">
          <el-input
            v-model="formData.topicContent"
            show-word-limit
            maxlength="100"
            clearable
            placeholder="请输入法律名称"
          />
        </el-form-item>
        <el-form-item label="章" prop="chapterContent">
          <el-input
            v-model="formData.chapterContent"
            show-word-limit
            maxlength="100"
            clearable
            placeholder="请输入法律名称"
          />
        </el-form-item>
        <el-form-item label="节" prop="jointContent">
          <el-input
            v-model="formData.jointContent"
            show-word-limit
            maxlength="100"
            clearable
            placeholder="请输入法律名称"
          />
        </el-form-item>
        <el-form-item label="条款" prop="provisionsContent">
          <el-input
            v-model="formData.provisionsContent"
            type="textarea"
            show-word-limit
            maxlength="5000"
            minlength="20"
            :rows="5"
            clearable
            placeholder="请输入条款具体内容"
          />
        </el-form-item>
        <el-form-item label="标签" prop="labelIds">
          <el-select v-model="formData.labelIds" multiple placeholder="请选择标签" style="width: 100%">
            <el-option v-for="item in tagsData" :key="item.id" :label="item.labelName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
