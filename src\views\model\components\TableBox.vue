<script setup lang="ts">
import SaveModelDialog from './SaveModelDialog.vue'
import { queryPageModelList, enableModel, disableModel, deleteModel } from '../api/index.ts'
import type { IQueryPageModelList, IModel, IModelType, IModelQueryData } from '../api/index.ts'

defineProps<{
  modelTypeList: IModelType[]
}>()

const saveModelDialogRef = ref<InstanceType<typeof SaveModelDialog>>()
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

const list = ref<IModel[]>()

// 分页获取模型列表
const getPageModelList = async (searchParams?: IModelQueryData) => {
  loading.value = true
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      data: searchParams || {},
    }
    const { data } = await queryPageModelList<IQueryPageModelList>(params)
    list.value = data.list || []
    pageNum.value = data.pageNum
    pageSize.value = data.pageSize
    total.value = +data.total
  } catch (err) {
    console.log('获取模型列表失败', err)
  } finally {
    loading.value = false
  }
}

// 编辑模型
const editModel = (id: string) => {
  saveModelDialogRef.value?.openDialog(id)
}

// 删除模型
const removeModel = (id: string) => {
  ElMessageBox.confirm('确定要删除该模型吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  }).then(async () => {
    // 调用删除接口
    console.log(id)
    try {
      const res = await deleteModel(id)
      console.log(res)
      if (res.code === '000000') {
        await getPageModelList()
        ElMessage.error('删除成功')
      }
    } catch (err) {
      console.error('删除失败', err)
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNum.value = 1
  getPageModelList()
}
const handleCurrentChange = (val: number) => {
  pageNum.value = val
  getPageModelList()
}
// 初始化
onMounted(() => {
  getPageModelList()
})

defineExpose({
  getPageModelList,
})

// 启用/禁用模型
const handleChangeEnabled = async (val: boolean, row: IModel) => {
  if (val === !!row.enableStatus) return

  const api = val ? enableModel : disableModel
  const res = await api(row.id)

  if (res.code === '000000') {
    ElMessage.success(val ? '启用成功' : '禁用成功')
    await getPageModelList()
  } else {
    ElMessage.error(val ? '启用失败' : '禁用失败')
    row.enableStatus = !val // 回滚状态
  }
}

const updateData = async () => {
  await getPageModelList()
}
</script>

<template>
  <div class="table-body">
    <el-table :data="list" row-key="id" v-loading="loading" max-height="calc(100vh - 210px)">
      <el-table-column prop="modelName" label="模型名称" :show-overflow-tooltip="true" />
      <el-table-column prop="modelTypeName" label="类型" />
      <el-table-column label="启用状态">
        <template #default="scope">
          <el-switch
            :model-value="!!scope.row.enableStatus"
            @change="(val) => handleChangeEnabled(val as boolean, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="180" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间">
        <template #default="scope">{{ scope.row.createTime ? scope.row.createTime.replace('T', ' ') : '-' }}</template>
      </el-table-column>
      <el-table-column label="更新时间">
        <template #default="scope">{{ scope.row.updateTime ? scope.row.updateTime.replace('T', ' ') : '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="primary" link @click="editModel(scope.row.id)">编辑</el-button>
          <el-button type="danger" link @click="removeModel(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="table-footer" v-show="list && list.length > 0">
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 40]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <SaveModelDialog ref="saveModelDialogRef" :model-type-list="modelTypeList" @updateList="updateData" />
</template>

<style scoped lang="scss">
////////// 表格高度自适应待处理
.table-body {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
.table-footer {
  flex-shrink: 0;
}
</style>
