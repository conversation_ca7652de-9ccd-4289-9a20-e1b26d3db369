import router from '@/router'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style

export default {
  install: () => {
    const appStore = useAppStore()
    NProgress.configure({ showSpinner: false }) // NProgress Configuration

    const whiteList = ['/login'] // no redirect whitelist

    router.beforeEach(async (to, from, next) => {
      console.log('临时修复构建报错', from)
      // start progress bar
      NProgress.start()
      const hasToken = !!appStore.token

      if (hasToken) {
        if (to.path === '/login') {
          next({ path: '/' })
          NProgress.done()
        } else {
          next()
        }
      } else {
        /* has no token*/
        if (whiteList.indexOf(to.path) !== -1) {
          // in the free login whitelist, go directly
          next()
        } else {
          // other pages that do not have permission to access are redirected to the login page.
          next('/login')
          NProgress.done()
        }
      }
    })

    router.afterEach(() => {
      // finish progress bar
      NProgress.done()
    })
  },
}
