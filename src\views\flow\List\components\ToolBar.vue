<script setup lang="ts">
import AddFlowDialog from './AddFlowDialog.vue'

const emits = defineEmits(['updateData'])

const addFlowDialogRef = ref<InstanceType<typeof AddFlowDialog>>()
const add = () => {
  addFlowDialogRef.value?.openDialog()
}

const updateData = () => {
  emits('updateData')
}
</script>

<template>
  <div>
    <div class="" align="right">
      <el-button type="primary" @click="add">新增流程</el-button>
    </div>
    <AddFlowDialog ref="addFlowDialogRef" @updateList="updateData" />
  </div>
</template>
