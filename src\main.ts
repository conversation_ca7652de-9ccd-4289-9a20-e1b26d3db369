import { createApp } from 'vue'
import router from './router'
import './styles/index.css'
import './plugins/iconfont/index.scss'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import permission from './permission'
import SvgIcons from '@/components/SvgIcons.vue'
import 'virtual:svg-icons-register'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
const app = createApp(App).use(router).use(pinia).use(permission)
app.component('svg-icon', SvgIcons)
app.mount('#app')

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
