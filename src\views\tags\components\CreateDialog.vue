<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    :title="dialogTitle"
    append-to-body
    :close-on-click-modal="false"
    width="500px"
    show-close
    @close="close"
  >
    <el-form ref="refForm" label-width="110px" :model="ruleForm" :rules="rules" label-position="top">
      <el-form-item label="标签名称" prop="labelName">
        <el-input v-model="ruleForm.labelName" show-word-limit maxlength="30" clearable placeholder="请输入标签名称" />
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
        <el-select
          v-model="ruleForm.bizType"
          placeholder="请选择业务类型"
          style="width: 100%"
          clearable
          :disabled="mode === 'edit'"
        >
          <el-option v-for="item in bizTypeOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <div class="footer">
        <el-button @click="close" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleCommit" :loading="loading">确认</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { saveLabel, updateLabel, getBizTypes, type BizTypeItem } from '@/services/labelApi'

// 定义 emits
const emit = defineEmits<{
  refresh: []
}>()

// 动态验证规则
const rules = computed(() => ({
  labelName: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 2, max: 30, message: '长度限制在2-30个字', trigger: 'blur' },
  ],
  bizType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
}))

const visible = ref(false)
const mode = ref<'add' | 'edit'>('add') // 模式：'add' 新增，'edit' 编辑
const editId = ref('') // 编辑时的ID
const loading = ref(false) // 提交loading状态

// 业务类型选项
const bizTypeOptions = ref<BizTypeItem[]>([])

const dialogTitle = computed(() => (mode.value === 'add' ? '新增标签' : '编辑标签'))

const iniData = {
  labelName: '',
  bizType: '',
}
const ruleForm = ref(iniData)

// 加载业务类型选项
const loadBizTypes = async () => {
  try {
    const response = await getBizTypes()
    bizTypeOptions.value = response.data || []
  } catch (error) {
    console.error('加载业务类型失败:', error)
  }
}

async function open(editData?: any) {
  // 每次打开弹窗时加载业务类型
  await loadBizTypes()

  if (editData) {
    // 编辑模式
    mode.value = 'edit'
    editId.value = editData.id
    ruleForm.value.labelName = editData.labelName
    ruleForm.value.bizType = editData.bizType
  } else {
    // 新增模式
    mode.value = 'add'
    editId.value = ''
    ruleForm.value = { ...iniData }
  }
  visible.value = true
}

function close() {
  refForm.value?.resetFields()
  visible.value = false
  // 重置模式状态
  mode.value = 'add'
  editId.value = ''
}

const refForm = ref<any>(null)

function handleCommit() {
  refForm.value?.validate(async (valid: any, fields: any) => {
    if (valid) {
      loading.value = true
      try {
        if (mode.value === 'edit') {
          // 编辑模式 - 调用编辑API
          const params = {
            id: editId.value,
            labelName: ruleForm.value.labelName,
            bizType: ruleForm.value.bizType,
          }
          await updateLabel(params)
          ElMessage.success('编辑成功！')
        } else {
          // 新增模式
          const params = {
            labelName: ruleForm.value.labelName,
            bizType: ruleForm.value.bizType,
          }
          await saveLabel(params)
          ElMessage.success('新增成功！')
        }
        emit('refresh')
        close()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        loading.value = false
      }
    } else {
      console.log('表单验证失败:', fields)
    }
  })
}

// 暴露给模板的方法
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 12px;
}
.dialog-wrap {
  .footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
