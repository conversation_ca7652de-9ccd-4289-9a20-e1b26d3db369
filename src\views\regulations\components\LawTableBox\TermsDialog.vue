<script lang="ts" setup>
const visible = ref(false)
const content = ref('aaaaa')
let aResolve: (value: unknown) => void

function open() {
  visible.value = true
  return new Promise((resolve) => {
    aResolve = resolve
  })
}

function close(flag?: boolean) {
  visible.value = false
  aResolve(!!flag)
}

function handleCommit() {
  close(true)
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="设置可见范围" v-model="visible" :close-on-click-modal="false" width="500px">
      <div v-html="content"></div>
      <template #footer>
        <div class="footer">
          <el-button type="primary" @click="handleCommit">去编辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
