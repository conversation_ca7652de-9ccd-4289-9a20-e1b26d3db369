<script setup lang="ts">
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import type { ILawSearchArgs } from './types'

const tableBox = ref<InstanceType<typeof TableBox>>()
function search(arg: ILawSearchArgs) {
  return tableBox.value?.query(arg)
}
</script>

<template>
  <div class="diff-wrap">
    <SearchBox @search="search"></SearchBox>
    <TableBox ref="tableBox"></TableBox>
  </div>
</template>

<style lang="scss" scoped>
.diff-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 0;
  padding: 1rem;
}
</style>
