<script setup lang="ts">
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import type { ISearchArgs } from './types'

const args: Partial<ISearchArgs> = {
  lawName: '',
  lawType: '',
  timeliness: '',
  tag: '',
  status: '',
  scope: '',
  source: '',
  creater: '',
}
const searchArgs = ref<Partial<ISearchArgs>>(args)

const tableBox = ref<InstanceType<typeof TableBox>>()
function search() {
  return tableBox.value?.reset()
}
function resetSearch() {
  searchArgs.value = args
  search()
}
</script>

<template>
  <div class="diff-wrap">
    <SearchBox
      :search-args="searchArgs"
      @search="search"
      @resetSearch="resetSearch"
      @update:status-option="search"
    ></SearchBox>
    <TableBox ref="tableBox" :search-args="searchArgs"></TableBox>
  </div>
</template>

<style lang="scss" scoped>
.diff-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 0;
}
</style>
