<script setup lang="ts">
import { queryWorkflowList } from '../../api/index.ts' // deleteWorkflowData
import type { IQueryWorkflowList, Ilist, IWorkflowQueryData } from '../../api/index.ts'

const router = useRouter()

const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

const list = ref<Ilist[]>()

const getAllFlows = async (searchParams?: IWorkflowQueryData) => {
  loading.value = true
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      data: searchParams || {},
    }
    const { data } = await queryWorkflowList<IQueryWorkflowList>(params)
    list.value = data.list || []
    pageNum.value = data.pageNum
    pageSize.value = data.pageSize
    total.value = +data.total
  } catch (err) {
    console.log('获取工作流列表失败', err)
  } finally {
    loading.value = false
  }
}

// 编辑工作流
const editWorkflow = (id: string) => {
  // 跳转要在新页签打开
  const routeData = router.resolve({
    name: 'WorkFlow',
    query: { id },
  })
  window.open(routeData.href, '_blank')
}

// const deleteWorkflow = (id: string) => {
//   ElMessageBox.confirm('确定要删除该工作流吗？', '提示', {
//     confirmButtonText: '确认',
//     cancelButtonText: '取消',
//   }).then(async () => {
//     try {
//       console.log('删除工作流')
//       const res = await deleteWorkflowData(id)
//       if (res.code === '000000') {
//         await getAllFlows()
//         ElMessage.error('删除成功')
//       }
//     } catch (err) {
//       console.error('删除失败', err)
//     }
//   })
// }

const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNum.value = 1
  getAllFlows()
}
const handleCurrentChange = (val: number) => {
  pageNum.value = val
  getAllFlows()
}
// 初始化
onMounted(() => {
  getAllFlows()
})

defineExpose({
  getAllFlows,
})
</script>

<template>
  <div class="table-body">
    <el-table :data="list" row-key="id" v-loading="loading" max-height="calc(100vh - 160px)">
      <el-table-column prop="appId" label="应用ID" />
      <el-table-column prop="wfName" label="流程名称" :show-overflow-tooltip="true" />
      <el-table-column prop="wfDescription" label="描述" :show-overflow-tooltip="true" />
      <el-table-column prop="createTime" label="创建时间">
        <template #default="scope">{{ scope.row.createTime ? scope.row.createTime.replace('T', ' ') : '-' }}</template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间">
        <template #default="scope">{{ scope.row.updateTime ? scope.row.updateTime.replace('T', ' ') : '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="primary" link @click="editWorkflow(scope.row.id)">编辑</el-button>
          <!-- <el-button type="danger" link @click="deleteWorkflow(scope.row.id)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="table-footer" v-show="list && list.length > 0">
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 40]"
      :total="total"
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
////////// 表格高度自适应待处理
.table-body {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
.table-footer {
  flex-shrink: 0;
}
</style>
