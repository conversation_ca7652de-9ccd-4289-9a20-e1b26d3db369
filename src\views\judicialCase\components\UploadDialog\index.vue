<script lang="ts" setup>
import TextOverTip from '@/components/TextOverTip.vue'
import ResultBox from './ResultBox.vue'

import type { FormInstance } from 'element-plus'
import { useTagService } from '@/views/useTagsService'
import { uploadFileTemporary, importCase } from '@/services/judicialCase'

const emit = defineEmits(['close'])
const { tagsData, queryTagsList } = useTagService()
const visible = ref(false)
const loading = ref(false)

const btnLoading = ref(false)
const limitSize = ref(10)
const acceptList = ref(['doc', 'docx', 'pdf'])
const isUploaded = ref(false)
const refForm = ref<FormInstance>()
const refItemUpload = ref()

interface IFileItem {
  fileName: string
  fileCode: string
}
interface IUploadResult {
  templateName: string
  templateUrl: string
}
const data = {
  fileList: [] as IFileItem[],
  tag: [] as string[],
}
const formData = ref(data)
function validateFileList(_rule: any, _value: any, callback: (arg0?: Error | undefined) => void) {
  if (formData.value.fileList.length) {
    callback()
    return
  }
  callback(new Error('请导入文件'))
}

function open(code: string) {
  visible.value = true
  formData.value.fileList.length = 0
  queryTagsList()
}

function close() {
  visible.value = false
  formData.value = data
  emit('close')
}

async function handleUploadFile(val: any) {
  if (val.size > limitSize.value * 1024 * 1024) {
    return ElMessage.error(`上传文件不能大于${limitSize.value}M：${val.name}`)
  }

  let fileSuffix = val.name.substring(val.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase()
  if (acceptList.value.indexOf(fileSuffix) === -1) {
    return ElMessage.error(`上传文件只能是 ${acceptList.value.join('、')}格式：${val.name}`)
  }

  loading.value = true

  const formdata = new FormData()
  formdata.append('file', val.raw)

  loading.value = false
  refItemUpload.value.clearValidate()
  const { code, data } = await uploadFileTemporary(formdata, {})
  console.log('uploadFileTemporary', data)

  if (code == '000000') {
    if (!Array.isArray(data)) {
      return
    }
    const list = (data as IUploadResult[]).map((item) => ({
      fileName: item.templateName,
      fileCode: item.templateUrl,
    }))
    formData.value.fileList.push(...list)
  } else {
    return ElMessage.error(`文件上传失败：${val.name}`)
  }
}
const addByImport = async (fileCodes: string[], labelIds: string[]) => {
  const params = {
    fileCodes,
    labelIds,
  }
  btnLoading.value = true
  const { code, message, data } = await importCase(params)
  btnLoading.value = false
  isUploaded.value = true
}
async function confirm() {
  refForm.value?.validate((valid) => {
    if (valid) {
      // close()
      // ElMessage.success('上传成功！')
      addByImport(
        formData.value.fileList.map((item) => item.fileCode),
        formData.value.tag.map((item) => item),
      )
    } else {
      console.warn('error')
    }
  })
}

function handleDel(idx: number) {
  formData.value.fileList.splice(idx, 1)
}

defineExpose({
  open,
})
</script>

<template>
  <el-dialog
    title="导入司法案例"
    v-model="visible"
    :close-on-click-modal="false"
    width="640px"
    :destroy-on-close="true"
  >
    <el-form v-if="!isUploaded" ref="refForm" label-position="top" :model="formData">
      <el-form-item
        ref="refItemUpload"
        label="导入文件"
        :rules="{ validator: validateFileList, required: true, trigger: 'change' }"
        prop="fileList"
      >
        <el-upload
          ref="upload"
          class="file-upload"
          :auto-upload="false"
          :action="'#'"
          :on-change="handleUploadFile"
          :show-file-list="false"
          drag
          multiple
          :accept="'.doc,.docx,.pdf'"
        >
          <span class="upload-tip">点击或将文件拖拽到这里上传</span>
          <span class="upload-intro"
            >支持{{ acceptList.join('、') }}格式，单个文件&lt;{{ limitSize }}M；支持批量上传</span
          >
        </el-upload>
        <div class="upload-list">
          <div v-for="(item, idx) in formData.fileList" :key="idx" class="upload-item">
            <!-- <img class="icon-contract" src="@/assets/images/gpt/contract.svg" /> -->
            <span class="upload-item__center"><TextOverTip :content="item.fileName" placement="top" /></span>
            <el-icon class="el-icon" @click="handleDel(idx)"><CircleCloseFilled /></el-icon>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="选择标签（可选）">
        <el-select v-model="formData.tag" :multiple="true" placeholder="请选择法律法规" style="width: 100%">
          <el-option v-for="item in tagsData" :key="item.id" :label="item.labelName" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <ResultBox v-else />
    <template v-slot:footer>
      <div>
        <el-button v-if="!isUploaded" @click="close">取消</el-button>
        <el-button v-if="!isUploaded" type="primary" :loading="btnLoading" @click="confirm">确定</el-button>
        <el-button v-else type="primary" @click="close">确定</el-button>
      </div>
    </template>
    <div v-if="loading" v-loading="true" class="loading-mask"></div>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  min-height: 300px;
  padding: 30px 24px;
}
.file-upload {
  width: 100%;

  // padding: 0 10px;
  :deep(.el-upload) {
    width: 100%;
  }
  :deep(.el-upload-dragger) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    background-color: #f7f9ff;
  }
  .upload-tip {
    margin: 12px 0;
    font-size: 16px;
    font-weight: bold;
    color: #595959;
  }
  .upload-intro {
    font-size: 12px;
    color: #929292;
  }
}
.upload-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding-top: 14px;
  .upload-item {
    display: flex;
    align-items: center;
    width: calc(50% - 8px);
    height: 30px;
    padding: 0 12px;
    margin-bottom: 8px;
    background-color: #f8f8f8;
    border-radius: 4px;
    &:nth-child(2n + 1) {
      margin-right: 16px;
    }
    &__center {
      flex: 1;
      width: 0;
      padding: 0 8px;
      margin-top: 3px;
      font-size: 12px;
    }
    .el-icon {
      color: #bdbdbd;
      cursor: pointer;
    }
  }
}
.loading-mask {
  position: absolute;
  top: 50px;
  left: 0;
  z-index: 10;
  width: 100%;
  height: calc(100% - 50px);
}
</style>
