{"editor.wordWrap": "on", "eslint.format.enable": true, "editor.formatOnSave": true, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "stylelint.validate": ["css", "scss", "postcss", "vue"], "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig, .stylelint*, .commitlintrc*, .npmrc, .gitignore", "components.d.ts": "*.d.ts", ".env.production": ".env.production, .env.development, .env.pro"}, "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}