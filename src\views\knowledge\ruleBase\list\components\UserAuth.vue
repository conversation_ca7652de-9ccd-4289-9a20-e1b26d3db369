<template>
  <div>
    <span v-if="props.data.globallyVisibleFlag">全局可见</span>
    <el-button v-else-if="userNums > 0" v-hover-tips="{ node: props.data }" type="text">
      {{ userNums }}人可见
    </el-button>
    <span v-else class="no-users">无可见用户</span>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import HoverTips from '../directives/hoverTips'

// 在 Vue 3 的 script setup 中注册指令
const vHoverTips = HoverTips

// 定义类型接口
interface VisibleUser {
  id: string | number
  name: string
  [key: string]: any
}

interface UserData {
  globallyVisibleFlag: boolean
  visibleUsers?: VisibleUser[]
  id: string | number
  [key: string]: any
}

// 定义 props
interface Props {
  data: UserData
}

const props = defineProps<Props>()

// 使用计算属性替代 watch + ref
const userNums = computed(() => props.data.visibleUsers?.length || 0)
</script>

<style lang="scss" scoped>
.no-users {
  font-size: 12px;
  color: #999;
}
</style>
