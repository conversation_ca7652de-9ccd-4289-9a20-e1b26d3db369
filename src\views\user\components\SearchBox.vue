<script setup lang="ts">
import type { IUserQueryData } from '@/services/user.ts'

const emits = defineEmits(['search'])

const initData: IUserQueryData = {
  userName: '',
  realName: '', // 用户名称
  userStatus: undefined, // 用户状态：0-禁用，1-正常
}

const searchForm = ref<IUserQueryData>({ ...initData })

const handleReset = () => {
  searchForm.value = { ...initData }
  handleSearch()
}

const handleSearch = () => {
  const params: IUserQueryData = {
    ...searchForm.value,
  }
  emits('search', params)
}
</script>

<template>
  <div class="condition">
    <span class="condition-label">用户名称</span>
    <el-input v-model="searchForm.realName" clearable placeholder="请输入搜索内容" />
  </div>
  <div class="condition">
    <span class="condition-label">状态</span>
    <el-select v-model="searchForm.userStatus" placeholder="请选择用户状态" clearable>
      <el-option label="禁用" value="0" />
      <el-option label="启用" value="1" />
    </el-select>
  </div>
  <div class="btn-wrap">
    <el-button class="btn" type="primary" @click="handleSearch">查 询</el-button>
    <el-button class="btn" @click="handleReset">重 置</el-button>
  </div>
</template>
