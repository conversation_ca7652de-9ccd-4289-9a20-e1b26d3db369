<template>
  <div class="box-wrap">
    <div class="condition">
      <span class="condition-label">合同类型名称</span>
      <el-input
        v-model="searchForm.contractTypeName"
        clearable
        placeholder="请输入合同类型名称"
        @clear="handleSearch"
        @blur="handleSearch"
        @keyup.enter="handleSearch"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'

// 定义类型接口
interface SearchForm {
  contractTypeName: string
}

interface SearchParams {
  contractTypeName?: string
}

// 定义 emits
const emit = defineEmits<{
  search: [params: SearchParams]
}>()

const searchForm = ref<SearchForm>({
  contractTypeName: '',
})

async function handleSearch() {
  // 使用 nextTick 确保 v-model 绑定的数据已经更新
  await nextTick()

  const searchParams: SearchParams = {
    contractTypeName: searchForm.value.contractTypeName || undefined,
  }

  emit('search', searchParams)
}
</script>

<style lang="scss" scoped>
.box-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 1rem;
  padding: 1rem;
  background-color: #fff;
  .condition {
    display: flex;
    align-items: center;
    width: 25%;
    &-label {
      min-width: 100px;
      padding-right: 12px;
      color: #606266;
      text-align: right;
    }
    .el-input {
      width: 100%;
    }
  }
}
</style>
