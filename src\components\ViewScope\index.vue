<template>
  <el-dialog v-model="visible" title="设置可见范围" width="600px">
    <el-select v-model="scope" placeholder="请选择可见范围" clearable style="width: 100%">
      <el-option label="后台可见" value="1"></el-option>
      <el-option label="公开可见" value="2"></el-option>
    </el-select>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="commit">确定</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
const emit = defineEmits(['close'])
const visible = ref(false)
const commit = () => {
  if (!scope.value) {
    ElMessage.error('请选择可见范围')
    return
  }
  close()
  emit('close', scope.value)
}
const scope = ref('')
const open = () => {
  visible.value = true
}
const close = () => {
  visible.value = false
}
defineExpose({
  open,
  close,
})
</script>
