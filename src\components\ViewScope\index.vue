<template>
  <el-dialog v-model="visible" title="设置可见范围" width="600px">
    <el-select v-model="scope" placeholder="请选择可见范围" style="width: 100%">
      <el-option v-for="item in scopeData" :key="item.value" :label="item.name" :value="item.value"></el-option>
    </el-select>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="commit">确定</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useGeneralService } from '@/composables/useGeneralService'
const { loadShareScope, scopeData } = useGeneralService()
const emit = defineEmits(['close'])
const visible = ref(false)

const commit = () => {
  if (!scope.value && scope.value != 0) {
    ElMessage.error('请选择可见范围')
    return
  }
  close()
  emit('close', scope.value)
}
const scope = ref(0)

const open = (_scope: number) => {
  visible.value = true
  scope.value = _scope
  loadShareScope()
}
const close = () => {
  visible.value = false
}
defineExpose({
  open,
  close,
})
</script>
