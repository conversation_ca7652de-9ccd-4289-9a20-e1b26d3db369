import request from '@/services'
import type { IResponse } from '@/services'

export function queryLawRecord<T, U>(data: U): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/lawPage`,
    method: 'post',
    data,
  })
}

export function getLawById<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/law/${id}`,
    method: 'get',
  })
}
export function updateLawInfo<T>(data: any): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/update`,
    method: 'post',
    data,
  })
}

export function delLawInfoById<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/delete/${id}`,
    method: 'delete',
  })
}

export function getLawInfo<T>(data: any): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/lawDetailPage`,
    method: 'post',
    data,
  })
}

export function getLawInfoById<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/lawDetail/${id}`,
    method: 'post',
  })
}

export function updateLawInfoDetail<T>(data: any): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/updateDetail`,
    method: 'post',
    data,
  })
}

export function updateShareScope<T, U>(data: U): Promise<IResponse<T>> {
  return request({
    url: `/lawInfo/updateShareScope`,
    method: 'post',
    data,
  })
}
