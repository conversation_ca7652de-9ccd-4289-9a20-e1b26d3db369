<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import TableLayout from '@/components/TableLayout/index.vue'
import type { TableColumn } from '@/components/TableLayout/types'
import SearchBox from './components/SearchBox.vue'
import EditDialog from './components/EditDialog.vue'
import CreateDialog from './components/CreateDialog.vue'
import { queryRuleList, deleteRuleList, saveRuleList } from '@/services/ruleBaseApi'
import { getContractTypeList } from '@/services/contractApi'

const router = useRouter()

// 表格引用
const tableLayoutRef = ref()

// 搜索参数
const searchParams = ref({})

// 合同类型数据管理
const contractTypeTreeData = ref<any[]>([])

// 可见范围编辑相关
const editingVisibilityRowId = ref<string | null>(null)
const tempVisibilityValue = ref<boolean>(true)
const visibilitySelectRef = ref()

// 解析状态配置
const statusOption = ref([
  { value: '0', label: '解析中', color: 'var(--main-bg)' },
  { value: '1', label: '已完成', color: '#1B9275' },
  { value: '2', label: '解析失败', color: '#E6555E' },
])

// 表格列配置
const columns: TableColumn[] = [
  {
    prop: 'ruleListName',
    label: '清单名称',
    minWidth: 120,
    showOverflowTooltip: true,
    slot: 'nameColumn',
  },
  {
    prop: 'contractTypes',
    label: '适用合同类型',
    width: 150,
    slot: 'contractTypesColumn',
  },
  {
    prop: 'rulePosition',
    label: '审查立场',
    slot: 'rulePositionColumn',
  },
  {
    prop: 'analysisProgress',
    label: '处理状态',
    width: 150,
    slot: 'statusColumn',
  },
  {
    prop: 'createUser',
    label: '创建人',
  },
  {
    prop: 'globallyVisibleFlag',
    label: '可见范围',
    slot: 'visibilityColumn',
  },
  {
    prop: 'dataSource',
    label: '数据来源',
    slot: 'dataSourceColumn',
  },
  {
    prop: 'ruleRemark',
    label: '备注',
    width: 200,
  },
]

// 状态标签处理
const setTagStatus = (val: string, type: 'color' | 'label') => {
  const tag = statusOption.value.find((item) => item.value == val)
  return tag ? tag[type] : ''
}

// 搜索参数处理
const handleSearchParams = async (params: any) => {
  console.log('收到搜索参数:', params)
  searchParams.value = params
  console.log('更新后的 searchParams:', searchParams.value)

  // 使用 nextTick 确保响应式数据更新完成后再调用 refresh
  await nextTick()

  if (tableLayoutRef.value) {
    tableLayoutRef.value.clearSelection()
    tableLayoutRef.value.refresh()
  }
}

// 数据加载完成处理
const handleDataLoaded = (data: any) => {
  console.log('审查清单数据加载完成:', data)
}

// 刷新表格
const refreshTable = () => {
  if (tableLayoutRef.value) {
    tableLayoutRef.value.clearSelection()
    tableLayoutRef.value.refresh()
  }
}

// 跳转详情页
const handleGoDetail = (row: any) => {
  if (row.analysisProgress == 2) {
    ElMessage.error('文件解析失败, 请重新上传！')
    return
  }
  router.push({
    name: 'RuleDetail',
    query: {
      id: row.id,
    },
  })
}

// 开始编辑可见范围
const startEditVisibility = (row: any) => {
  editingVisibilityRowId.value = row.id
  tempVisibilityValue.value = row.globallyVisibleFlag

  // 下一帧聚焦到选择框
  nextTick(() => {
    if (visibilitySelectRef.value) {
      visibilitySelectRef.value.focus()
    }
  })
}

// 取消编辑可见范围
const cancelEditVisibility = () => {
  editingVisibilityRowId.value = null
  tempVisibilityValue.value = true
}

// 处理可见范围变更
const handleVisibilityChange = async (row: any) => {
  try {
    const params = {
      id: row.id,
      globallyVisibleFlag: tempVisibilityValue.value,
      ruleListName: row.ruleListName,
      rulePosition: row.rulePosition,
      ruleRemark: row.ruleRemark,
    }

    await saveRuleList(params)

    // 更新本地数据
    row.globallyVisibleFlag = tempVisibilityValue.value

    ElMessage.success(`已切换为${tempVisibilityValue.value ? '公开可见' : '后台可见'}`)

    // 退出编辑模式
    cancelEditVisibility()
  } catch (error) {
    console.error('切换可见范围失败:', error)
    ElMessage.error('切换可见范围失败')
    // 发生错误时也退出编辑模式
    cancelEditVisibility()
  }
}

// 删除处理
const handleDel = (row: any) => {
  ElMessageBox.confirm('删除当前审查清单，该分类下的所有数据也会删除，确定要删除吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      await deleteRuleList({ rulListId: row.id })
      ElMessage({
        type: 'success',
        message: '删除成功!',
      })
      refreshTable()
    })
    .catch(() => {
      console.log('取消删除')
    })
}

// 弹窗引用
const refEdit = ref<any>(null)
const refCreate = ref<any>(null)

// 打开弹窗
const handleOpenDialog = (row?: any) => {
  if (row) {
    refEdit.value?.open(row)
  } else {
    // 新增模式 - 调用 CreateDialog
    refCreate.value?.open()
  }
}

// AI生成
const handleGenerative = () => {
  refCreate.value?.open()
}

// 合同类型数据加载
async function loadContractTypeTree() {
  if (contractTypeTreeData.value.length > 0) return // 已加载过则不重复加载

  try {
    const response = await getContractTypeList({ contractTypeName: '' })
    console.log('合同类型API响应:', response)

    // 从API响应中提取数据
    const responseData = response.data as any
    let rawData = []
    if (responseData && responseData.records) {
      rawData = responseData.records
    } else {
      rawData = responseData || []
    }

    console.log('合同类型原始数据:', rawData)
    // 将平铺数据转换为树形结构
    contractTypeTreeData.value = buildTreeData(rawData)
    console.log('合同类型树形数据:', contractTypeTreeData.value)
  } catch (error) {
    console.error('加载合同类型失败:', error)
    ElMessage.error('加载合同类型失败')
  }
}

// 组件挂载时加载合同类型数据
onMounted(() => {
  loadContractTypeTree()
})

// 构建树形数据结构
function buildTreeData(data: any[]): any[] {
  if (!Array.isArray(data)) return []

  // 如果数据已经是树形结构，直接返回
  if (data.some((item) => item.children)) {
    return data
  }

  // 如果是平铺数据，转换为树形结构
  const map = new Map()
  const roots: any[] = []

  // 先创建所有节点的映射
  data.forEach((item) => {
    map.set(item.id, {
      ...item,
      children: [],
    })
  })

  // 构建父子关系
  data.forEach((item) => {
    const node = map.get(item.id)
    if (item.parentId && map.has(item.parentId)) {
      map.get(item.parentId).children.push(node)
    } else {
      roots.push(node)
    }
  })

  return roots
}
</script>
<template>
  <SearchBox
    :status-option="statusOption"
    :contract-type-tree-data="contractTypeTreeData"
    @search="handleSearchParams"
  />
  <div style="padding: 16px; text-align: right">
    <el-button @click="handleOpenDialog">手动新增</el-button>
    <el-button type="primary" @click="handleGenerative">AI解析生成</el-button>
  </div>
  <TableLayout
    ref="tableLayoutRef"
    :api-function="queryRuleList"
    :api-params="searchParams"
    :columns="columns"
    :poll-interval="30000"
    @data-loaded="handleDataLoaded"
  >
    <!-- 清单名称列插槽 -->
    <template #nameColumn="{ row }">
      <span class="go-detail" @click="handleGoDetail(row)">{{ row.ruleListName }}</span>
    </template>

    <!-- 适用合同类型列插槽 -->
    <template #contractTypesColumn="{ row }">
      {{ row.contractTypes || '-' }}
    </template>

    <!-- 审查立场列插槽 -->
    <template #rulePositionColumn="{ row }">
      {{ row.rulePosition || '-' }}
    </template>

    <!-- 处理状态列插槽 -->
    <template #statusColumn="{ row }">
      <span class="status-dot" :style="{ 'background-color': setTagStatus(row.analysisProgress, 'color') }"></span>
      <span>{{ setTagStatus(row.analysisProgress, 'label') }}</span>
    </template>

    <!-- 可见范围列插槽 -->
    <template #visibilityColumn="{ row }">
      <span
        v-if="editingVisibilityRowId !== row.id"
        class="visibility-toggle"
        @click="startEditVisibility(row)"
        :title="'点击编辑可见范围'"
      >
        {{ row.globallyVisibleFlag ? '公开可见' : '后台可见' }}
      </span>
      <el-select
        v-else
        v-model="tempVisibilityValue"
        size="small"
        @change="handleVisibilityChange(row)"
        @blur="cancelEditVisibility"
        style="width: 100px"
        ref="visibilitySelectRef"
      >
        <el-option label="公开可见" :value="true" />
        <el-option label="后台可见" :value="false" />
      </el-select>
    </template>

    <!-- 数据来源列插槽 -->
    <template #dataSourceColumn="{ row }">
      {{ row.dataSource || '-' }}
    </template>

    <!-- 操作列插槽 -->
    <template #actions="{ row }">
      <div>
        <el-button
          type="text"
          :disabled="row.analysisProgress === 2 || row.analysisProgress === 0"
          @click="handleOpenDialog(row)"
        >
          编辑
        </el-button>
        <el-button type="text" @click="handleDel(row)">删除</el-button>
      </div>
    </template>
  </TableLayout>

  <!-- 弹窗组件 -->
  <EditDialog ref="refEdit" :contract-type-tree-data="contractTypeTreeData" @refresh="refreshTable" />
  <CreateDialog ref="refCreate" :contract-type-tree-data="contractTypeTreeData" @refresh="refreshTable" />
</template>
<style lang="scss" scoped>
.go-detail {
  color: var(--main-bg);
  cursor: pointer;
}
.status-dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-right: 5px;
  margin-bottom: 2px;
  border-radius: 50%;
}
.visibility-toggle {
  color: var(--main-bg);
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.warning-text {
  color: #e6555e !important;
}
</style>
