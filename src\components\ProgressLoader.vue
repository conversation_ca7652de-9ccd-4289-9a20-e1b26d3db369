<template>
  <div v-loading="loading" :element-loading-text="progressText">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 定义 props 接口
interface Props {
  // 获取进度的API函数
  checkProgress: (params: any) => Promise<{ data: number }>
  // 获取进度的参数
  checkParams: string | number | object
  // 进度检查间隔(ms)
  checkInterval?: number
  // 虚拟进度更新间隔(ms)
  fakeUpdateInterval?: number
  // 完成后的延迟(ms)
  completeDelay?: number
}

// 定义 emits
interface Emits {
  complete: []
}

// 定义 props 默认值
const props = withDefaults(defineProps<Props>(), {
  checkInterval: 2000,
  fakeUpdateInterval: 500,
  completeDelay: 500,
})

// 定义 emits
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(true)
const progress = ref(0)
const progressText = ref('0%')

// 定时器变量
let pollingTimer: number | null = null
let fakeProgressTimer: number | null = null

// 开始虚拟进度
const startFakeProgress = () => {
  fakeProgressTimer = setInterval(() => {
    if (progress.value < 99) {
      progress.value += Math.floor(Math.random() * 30) + 1
      progressText.value = `${progress.value}%`
      if (progress.value > 99) {
        progress.value = 99
        progressText.value = `${progress.value}%`
      }
    }
  }, props.fakeUpdateInterval)
}

// 轮询检查数据状态
const pollDataStatus = async () => {
  try {
    const { data } = await props.checkProgress(props.checkParams)
    if (data === 1) {
      handleLoadingComplete()
    } else {
      pollingTimer = setTimeout(() => {
        pollDataStatus()
      }, props.checkInterval)
    }
  } catch (error) {
    console.error('检查解析状态失败:', error)
    handleLoadingComplete()
  }
}

// 处理加载完成
const handleLoadingComplete = () => {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer)
  }
  if (pollingTimer) {
    clearTimeout(pollingTimer)
  }
  progress.value = 100
  progressText.value = `${progress.value}%`

  // 短暂延迟后隐藏加载状态
  setTimeout(() => {
    loading.value = false
    emit('complete')
  }, props.completeDelay)
}

onMounted(() => {
  startFakeProgress()
  pollDataStatus()
})

onBeforeUnmount(() => {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer)
  }
  if (pollingTimer) {
    clearTimeout(pollingTimer)
  }
})
</script>
