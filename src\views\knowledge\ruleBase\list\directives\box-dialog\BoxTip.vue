<template>
  <div class="cooperate-dialog" :style="{ top: node.top + 'px', left: node.left + 'px' }">
    <transition name="ui-dialog-fade" mode="out-in">
      <div v-if="showFlag" class="dialog-content">
        <div class="head">
          <span>用户列表</span>
          <span v-if="list.length">共{{ list.length }}人</span>
        </div>
        <div v-loading="loading" class="cooperate-list-box">
          <el-scrollbar style="width: 100%; height: 100%">
            <div style="height: 100%; max-height: 100px">
              <div v-for="(item, index) in list" :key="index" class="cooperate-node">
                <span class="name" :title="item.realName">{{ item.realName }}</span>
              </div>
              <div v-if="!list.length" class="no-result">暂无数据</div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义类型接口
interface User {
  realName: string
  [key: string]: any
}

interface Node {
  visibleUsers?: User[]
  top: number
  left: number
  [key: string]: any
}

// 响应式数据
const list = ref<User[]>([])
const node = ref<Node>({
  visibleUsers: [],
  top: 0,
  left: 0,
})
const loading = ref(false)
const showFlag = ref(false)
const urgeFlag = ref(false)

// 方法
const show = () => {
  showFlag.value = true
  searchList()
}

const searchList = async () => {
  list.value = node.value.visibleUsers || []
}

// 更新节点数据的方法
const updateNode = (newNode: Partial<Node>) => {
  Object.assign(node.value, newNode)
}

// 暴露方法给外部调用
defineExpose({
  show,
  updateNode,
  list,
  node,
  loading,
  showFlag,
  urgeFlag,
})
</script>
<style lang="scss" scoped>
.cooperate-list-box {
  position: relative;
  min-height: 42px;
  max-height: 190px;
  padding: 5px 0;
  overflow: hidden scroll;
}

.dialog-content {
  .head {
    display: flex;
    justify-content: space-between;
    height: 34px;
    padding: 0 13px;
    font-size: 12px;
    font-weight: 400;
    line-height: 34px;
    color: #595959;
    border-bottom: 1px solid #e9e9e9;
  }
}

.no-result {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 12px;
  color: #929292;
}

.cooperate-dialog {
  position: fixed;
  z-index: 9999;
  width: 235px;
  background: #fff;
  background-color: rgb(255 255 255 / 100%); /* 确保背景完全不透明 */
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 10%);

  .cooperate-node {
    position: relative;
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 12px 0 16px;
    overflow-y: hidden;
  }

  .name {
    display: inline-block;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    color: #262626;
    text-align: left;
    white-space: nowrap;
  }
}
</style>
