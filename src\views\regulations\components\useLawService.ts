import { delLawInfoById, queryLawRecord, updateLawInfo, updateShareScope } from '@/services/regulations'
import type { ILawParams, ILawRecord, ILawResponse, ILawSearchArgs } from '../types'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

export function useLawService() {
  const loading = ref(false)
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const tableData = ref<ILawRecord[]>([])

  const search = (arg?: ILawSearchArgs) => {
    page.value = 1
    console.log('search')
    getTableData(arg)
  }

  const handleSizeChange = (val: number) => {
    pageSize.value = val
    page.value = 1
    getTableData()
  }

  const handleCurrentChange = (val: number) => {
    page.value = val
    getTableData()
  }

  const handleDel = (id: string) => {
    ElMessageBox.confirm('确认删除这条记录吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      showClose: false,
    })
      .then(async () => {
        const { code, message } = await delLawInfoById(id)
        if (code === RESPONSE_CODE_SUCCESS) {
          getTableData()
          ElMessage.success('删除成功')
        } else {
          ElMessage.error(message)
        }
      })
      .catch(() => {
        console.log('取消删除比对记录')
      })
  }

  const getTableData = async (args?: ILawSearchArgs) => {
    loading.value = true
    const params = {
      pageNum: page.value,
      pageSize: pageSize.value,
      data: {
        ...args,
      },
    }

    try {
      const res = await queryLawRecord<ILawResponse, ILawParams>(params)
      const { data } = res
      console.log(data)
      tableData.value = data.list
      total.value = Number(data.total)
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const update = async (postData: any) => {
    const { code, message } = await updateLawInfo(postData)
    if (code === RESPONSE_CODE_SUCCESS) {
      search()
    } else {
      ElMessage.error(message)
    }
  }

  const updateScope = async (postData: any) => {
    const { code, message } = await updateShareScope(postData)
    if (code === RESPONSE_CODE_SUCCESS) {
      search()
    } else {
      ElMessage.error(message)
    }
  }

  return {
    search,
    updateScope,
    tableData,
    total,
    pageSize,
    page,
    loading,
    update,
    handleDel,
    handleCurrentChange,
    handleSizeChange,
  }
}
