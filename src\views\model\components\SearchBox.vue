<script setup lang="ts">
import type { IModelType, IModelQueryData } from '../api/index.ts'

defineProps<{
  modelTypeList: IModelType[]
}>()

const emits = defineEmits(['search'])
const initData: IModelQueryData = {
  modelName: '',
  modelType: undefined, // 模型类型：0-文本生成 1-文本嵌入模型 2-视觉嵌入模型 ...
  remark: '',
}

const searchForm = ref<IModelQueryData>({ ...initData })

const handleReset = () => {
  searchForm.value = { ...initData }
  handleSearch()
}

const handleSearch = () => {
  const params: IModelQueryData = {
    ...searchForm.value,
  }
  emits('search', params)
}
</script>

<template>
  <div class="condition">
    <span class="condition-label">模型名称</span>
    <el-input v-model="searchForm.modelName" clearable placeholder="请输入搜索内容" />
  </div>
  <div class="condition">
    <span class="condition-label">类型</span>
    <el-select v-model="searchForm.modelType" placeholder="请选择模型类型" clearable>
      <el-option v-for="item in modelTypeList" :label="item.modelTag" :value="item.value" :key="item.modelTag" />
    </el-select>
  </div>
  <div class="condition">
    <span class="condition-label">模型备注</span>
    <el-input v-model="searchForm.remark" clearable placeholder="请输入搜索内容" />
  </div>
  <div class="btn-wrap">
    <el-button class="btn" type="primary" @click="handleSearch">查 询</el-button>
    <el-button class="btn" @click="handleReset">重 置</el-button>
  </div>
</template>
