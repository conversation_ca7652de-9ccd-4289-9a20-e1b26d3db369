<script setup lang="ts">
import { queryLawRecord } from '@/services/regulations'
import TextFold from './TextFold.vue'
// import type { ICompareResponseList } from '@/services/textComparison'
import type { ILawTableParams, ILawTableRecord, ILawTableResponse } from '../../types'

defineProps<{
  currentLawId: string
}>()

const searchArgs = ref({ content: '' })

const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<ILawTableRecord[]>([])

const getTableData = async () => {
  loading.value = true
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    data: {
      content: '',
    },
  }

  try {
    const res = await queryLawRecord<ILawTableResponse, ILawTableParams>(params)
    const { data } = res
    tableData.value = data.result
    total.value = data.count
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}
getTableData()

const reset = () => {
  page.value = 1
  getTableData()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  getTableData()
}

const handleCurrentChange = (val: number) => {
  page.value = val
  getTableData()
}

const handleDel = (id: string) => {
  ElMessageBox.confirm('确认删除这条记录吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    showClose: false,
  })
    .then(async () => {
      console.log(id)
      // await deleteSemanticsRecord(id)
      getTableData()
      ElMessage({
        type: 'success',
        message: '删除成功!',
      })
    })
    .catch(() => {
      console.log('取消删除比对记录')
    })
}

const handleEdit = async (id: string) => {
  // const flag = await refEditDialog.value.open(id)
  // console.log(flag)
}
</script>

<template>
  <div class="law-table-wrap">
    <div class="search-wrap">
      <div class="condition">
        <span class="condition-label">内容</span>
        <el-input v-model="searchArgs.content" @keyup.enter="getTableData" clearable placeholder="请输入" />
      </div>
      <div class="btn-wrap">
        <el-button type="primary" @click.stop="getTableData">查 询</el-button>
        <el-button @click="reset">重 置</el-button>
      </div>
    </div>
    <el-table
      ref="table"
      v-loading="loading"
      height="100%"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column label="法律名称" :show-overflow-tooltip="true" min-width="200">
        <template v-slot="scope">
          <span>{{ scope.row.lawName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="编">
        <template v-slot="scope">
          <span>{{ scope.row.book || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章">
        <template v-slot="scope">
          <span>{{ scope.row.chapter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节">
        <template v-slot="scope">
          <span>{{ scope.row.festival }}</span>
        </template>
      </el-table-column>
      <el-table-column label="条款" width="200">
        <template v-slot="scope">
          <!-- <span>{{ scope.row.terms }}abceegabceegfabceegfabceegfabceegfabceegfabceegfabceegfabceegff</span> -->
          <TextFold
            text="abceegabceeg我门额的蹦呢fabceegfab我门额的蹦呢我门额的蹦呢我门额的蹦呢我门额的蹦呢我门额的蹦呢我门额的蹦呢"
            id="aaa"
          />
        </template>
      </el-table-column>
      <el-table-column label="标签">
        <template v-slot="scope">
          <span>{{ scope.row.tag }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template v-slot="scope">
          <el-button-group class="el-button-right-line">
            <el-button type="primary" link @click="handleEdit(scope.row.id)">编辑</el-button>
            <el-button type="primary" link @click="handleDel(scope.row.id)">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="page"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped>
.law-table-wrap {
  flex: 1;
  width: 100%;
  height: 100%;
  padding-bottom: 42px;

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 12px;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }
  .search-wrap {
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
    padding: 16px 0;
    background-color: #fff;
    .condition {
      display: flex;
      align-items: center;
      width: 33.3%;
      &-label {
        width: 90px;
        padding-left: 12px;
        font-size: 14px;
        color: #606266;
      }
      .el-input {
        width: 100%;
      }
    }
    .btn-wrap {
      display: flex;
      flex: 1;
      justify-content: flex-start;
      padding-left: 12px;
    }
  }
}
</style>
