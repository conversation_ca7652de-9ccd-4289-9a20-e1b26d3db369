<script setup lang="ts">
import { getLawInfo } from '@/services/regulations'
import TextFold from './TextFold.vue'
import EditDialog from './EditDialog.vue'
import { useRoute } from 'vue-router'
import useWindowResize from '@/composables/useWindowResize'
import type { ILawTableRecord, ILawTableResponse } from '../../types'
const route = useRoute()
const { height } = useWindowResize()
const searchArgs = ref({ provisionsContent: '', lawId: '' })
const dialogVisible = ref(false)
const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<ILawTableRecord[]>([])

const getLawInfoTableData = async () => {
  loading.value = true
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    data: {
      ...searchArgs.value,
    },
  }

  try {
    const res = await getLawInfo<ILawTableResponse>(params)
    const { data } = res
    tableData.value = data.list
    total.value = Number(data.total)
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const reset = () => {
  page.value = 1
  searchArgs.value.provisionsContent = ''
  getLawInfoTableData()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  getLawInfoTableData()
}

const handleCurrentChange = (val: number) => {
  page.value = val
  getLawInfoTableData()
}

const handleDel = (id: string) => {
  ElMessageBox.confirm('确认删除这条记录吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    showClose: false,
  })
    .then(async () => {
      console.log(id)
      // await deleteSemanticsRecord(id)
      getLawInfoTableData()
      ElMessage({
        type: 'success',
        message: '删除成功!',
      })
    })
    .catch(() => {
      console.log('取消删除比对记录')
    })
}

const editDailogRef = ref()
const handleEdit = async (row?: any) => {
  const law = row ? row : currentLaw.value
  editDailogRef.value.open(law)
}

const currentLaw = ref()

const showMoreText = (row: any) => {
  currentLaw.value = row
  dialogVisible.value = true
}
const dialogClose = () => {
  dialogVisible.value = false
  currentLaw.value = null
}

const search = () => {
  page.value = 1
  getLawInfoTableData()
}
const maxHeight = ref(400)
onMounted(() => {
  console.log(route.params)
  const { id } = route.params
  maxHeight.value = height.value - 250
  searchArgs.value.lawId = id as string
  search()
})
</script>

<template>
  <div class="law-table-wrap">
    <div class="search-wrap">
      <div class="condition">
        <span class="condition-label">内容</span>
        <el-input v-model="searchArgs.provisionsContent" @keyup.enter="search" clearable placeholder="请输入" />
      </div>
      <div class="btn-wrap">
        <el-button type="primary" @click.stop="search">查 询</el-button>
        <el-button @click="reset">重 置</el-button>
      </div>
    </div>
    <el-table
      ref="table"
      v-loading="loading"
      :max-height="maxHeight"
      height="100%"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column label="法律名称" width="200" show-overflow-tooltip>
        <template v-slot="scope">
          <span>{{ scope.row.lawName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="篇" show-overflow-tooltip>
        <template v-slot="scope">
          <span>{{ scope.row.topicContent || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="章" show-overflow-tooltip>
        <template v-slot="scope">
          <span>{{ scope.row.chapterContent }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节" show-overflow-tooltip>
        <template v-slot="scope">
          <span>{{ scope.row.jointContent }}</span>
        </template>
      </el-table-column>
      <el-table-column label="条款" width="200">
        <template v-slot="{ row }">
          <TextFold
            :text="row.provisionsContent"
            @more="
              () => {
                showMoreText(row)
              }
            "
          />
        </template>
      </el-table-column>
      <el-table-column label="标签">
        <template v-slot="scope">
          <el-tag v-for="(tag, index) in scope.row.labels" :key="index">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <el-button-group class="el-button-right-line">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <!-- <el-button type="primary" link @click="handleDel(row.id)">删除</el-button> -->
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="page"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
    <el-dialog v-model="dialogVisible" title="查看条款" width="70vw" @close="dialogClose">
      <div>{{ currentLaw?.provisionsContent }}</div>
      <template #footer>
        <el-button @click="dialogClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit()">去编辑</el-button>
      </template>
    </el-dialog>
    <EditDialog ref="editDailogRef" @close="search"></EditDialog>
  </div>
</template>

<style lang="scss" scoped>
.law-table-wrap {
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 1rem;
  padding-bottom: 42px;

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }
  .search-wrap {
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
    padding: 16px 0;
    background-color: #fff;
    .condition {
      display: flex;
      align-items: center;
      width: 33.3%;
      &-label {
        width: 90px;
        padding-left: 12px;
        font-size: 14px;
        color: #606266;
      }
      .el-input {
        width: 100%;
      }
    }
    .btn-wrap {
      display: flex;
      flex: 1;
      justify-content: flex-start;
      padding-left: 12px;
    }
  }
}
</style>
