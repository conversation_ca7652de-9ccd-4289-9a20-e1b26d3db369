<script setup lang="ts">
import { type FormInstance } from 'element-plus'
import { editPassword } from '@/services/login.ts'
import type { IEditPasswordParams } from '@/services/login.ts'

const title = '修改密码'

const visible = ref(false)
const ruleFormRef = ref<FormInstance>()
interface IEditPassword extends IEditPasswordParams {
  confirmPassword: string
}

const formData = ref<IEditPassword>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const rules = {
  oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (!value) return callback()

        const lengthValid = value.length >= 8 && value.length <= 16
        if (!lengthValid) return callback(new Error('新密码长度需为8-16个字符'))

        // 至少包含两种字符：大写、小写、数字、特殊字符
        const types = [
          /[A-Z]/.test(value),
          /[a-z]/.test(value),
          /\d/.test(value),
          /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value),
        ]
        const typeCount = types.filter(Boolean).length
        if (typeCount < 2) return callback(new Error('新密码需包含大小写字母、数字或特殊字符中的任意两种'))

        if (value === formData.value.oldPassword) {
          return callback(new Error('新密码不能与原密码相同'))
        }

        callback()
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请再次确认新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (value !== formData.value.newPassword) {
          return callback(new Error('两次输入的密码不一致'))
        }
        callback()
      },
      trigger: 'blur',
    },
  ],
}

const openDialog = async () => {
  visible.value = true
  formData.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  }
}

const closeDialog = () => {
  visible.value = false
  ruleFormRef.value?.resetFields()
}

// 修改密码
const editPwd = async () => {
  await ruleFormRef.value?.validate()

  const params: IEditPasswordParams = formData.value
  try {
    const res = await editPassword(params)
    if (res.code == '000000') {
      ElMessage.success('密码成功')
      closeDialog()
    } else {
      console.log('响应不成', res)
      ElMessage.error(res.message)
    }
  } catch (error) {
    // ElMessage.error('密码修改失败')
  }
}

defineExpose({
  openDialog,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" width="600px" @close="closeDialog">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" class="right-form" label-position="top">
      <el-form-item label="原密码" prop="oldPassword">
        <el-input v-model="formData.oldPassword" type="password" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="formData.newPassword" type="password" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="再次确认" prop="confirmPassword">
        <el-input v-model="formData.confirmPassword" type="password" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="editPwd">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
