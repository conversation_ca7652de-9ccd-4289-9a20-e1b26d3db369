<script lang="ts" setup>
import type { FormInstance } from 'element-plus'

const visible = ref(false)
let aResolve: (value: unknown) => void

function open() {
  visible.value = true
  return new Promise((resolve) => {
    aResolve = resolve
  })
}

function close(flag?: boolean) {
  visible.value = false
  aResolve(!!flag)
}

const refForm = ref<FormInstance>()

const formData = ref({
  lawName: '',
  lawType: '',
  timeliness: '',
  tag: '',
})

const rules = {
  lawName: [{ required: true, message: '请输入法律名称', trigger: 'blur' }],
  lawType: [{ required: true, message: '请选择法律类型', trigger: 'change' }],
  timeliness: [{ required: true, message: '请选择法时效性', trigger: 'change' }],
}

const options = [
  { value: 1, label: '1' },
  { value: 2, label: '2' },
]

function handleCommit() {
  close(true)
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="编辑" v-model="visible" :close-on-click-modal="false" width="500px">
      <el-form ref="refForm" label-width="110px" :model="formData" :rules="rules">
        <el-form-item label="法律名称" prop="lawName">
          <el-input v-model="formData.lawName" show-word-limit maxlength="30" clearable placeholder="请输入法律名称" />
        </el-form-item>
        <el-form-item label="法律法规" prop="lawType">
          <el-select v-model="formData.lawType" placeholder="请选择法律法规" style="width: 100%">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="时效性" prop="timeliness">
          <el-select v-model="formData.timeliness" placeholder="请选择失效性" style="width: 100%">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-select v-model="formData.tag" placeholder="请选择失效性" style="width: 100%">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
