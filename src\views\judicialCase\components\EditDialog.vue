<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import { useTagService } from '@/views/useTagsService'
import type { ILawCase } from '../types'
import { getLawCaseById, updateLawCase } from '@/services/judicialCase'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
const emit = defineEmits(['close'])
const { tagsData, queryTagsList } = useTagService()
const visible = ref(false)

function open(id: string) {
  visible.value = true
  refForm.value?.clearValidate()
  queryTagsList()
  queryDetail(id)
}

function close() {
  visible.value = false
  refForm.value?.resetFields()
}

const queryDetail = async (id: string) => {
  formData.value.id = id
  const { code, message, data } = await getLawCaseById<ILawCase>(id)
  if (code === RESPONSE_CODE_SUCCESS) {
    const { caseNumber, caseName, caseReason, courtName, caseLocation, judgeTime, labelIds } = data as ILawCase
    formData.value.caseLocation = caseLocation
    formData.value.caseName = caseName
    formData.value.caseNumber = caseNumber
    formData.value.caseReason = caseReason
    formData.value.courtName = courtName
    formData.value.judgeTime = judgeTime
    formData.value.labelIds = labelIds
  } else {
    ElMessage.error(message)
  }
}

const refForm = ref<FormInstance>()

const formData = ref({
  id: '',
  caseNumber: '',
  caseName: '',
  caseReason: '',
  courtName: '',
  caseLocation: '',
  judgeTime: '',
  labelIds: [] as string[],
})

const rules = {
  caseNumber: [{ required: true, message: '请输入案号', trigger: 'blur' }],
  caseName: [{ required: true, message: '请输入案例名称', trigger: 'blur' }],
  caseReason: [{ required: true, message: '请输入案由', trigger: 'blur' }],
}
const update = async (postData: any) => {
  const { code, message } = await updateLawCase(postData)
  if (code === RESPONSE_CODE_SUCCESS) {
    close()
    emit('close')
  } else {
    ElMessage.error(message)
  }
}
function handleCommit() {
  refForm.value?.validate((isValid: boolean) => {
    if (isValid) {
      update(formData.value)
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="编辑" v-model="visible" :close-on-click-modal="false" width="500px">
      <el-form ref="refForm" label-width="80px" :model="formData" :rules="rules">
        <el-form-item label="案号" prop="caseNumber">
          <el-input v-model="formData.caseNumber" show-word-limit maxlength="50" clearable placeholder="请输入案号" />
        </el-form-item>
        <el-form-item label="案例名称" prop="caseName">
          <el-input
            v-model="formData.caseName"
            show-word-limit
            maxlength="100"
            clearable
            placeholder="请输入案例名称"
          />
        </el-form-item>
        <el-form-item label="案由" prop="caseReason">
          <el-input v-model="formData.caseReason" show-word-limit maxlength="50" clearable placeholder="请输入案由" />
        </el-form-item>
        <el-form-item label="审理法院" prop="courtName">
          <el-input
            v-model="formData.courtName"
            show-word-limit
            maxlength="50"
            clearable
            placeholder="请输入审理法院"
          />
        </el-form-item>

        <el-form-item label="裁判日期" prop="judgeTime">
          <el-date-picker
            v-model="formData.judgeTime"
            type="datetime"
            placeholder="请选择裁判日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="适用地域" prop="caseLocation">
          <el-input
            v-model="formData.caseLocation"
            show-word-limit
            maxlength="50"
            clearable
            placeholder="请输入适用地域"
          />
        </el-form-item>
        <el-form-item label="标签" prop="labelIds">
          <el-select v-model="formData.labelIds" :multiple="true" placeholder="请选择标签" style="width: 100%">
            <el-option v-for="item in tagsData" :key="item.id" :label="item.labelName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="handleCommit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
