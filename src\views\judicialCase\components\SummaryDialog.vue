<script lang="ts" setup>
const visible = ref(false)
const content = ref('aaaaa')
function open() {
  visible.value = true
}

function close(flag?: boolean) {
  visible.value = false
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="查看案例概要" v-model="visible" :close-on-click-modal="false" width="500px">
      <div v-html="content"></div>
    </el-dialog>
  </div>
</template>
