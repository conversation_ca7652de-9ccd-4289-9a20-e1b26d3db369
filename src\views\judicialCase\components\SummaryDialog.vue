<script lang="ts" setup>
const visible = ref(false)
const content = ref('')
function open(text: string) {
  visible.value = true
  content.value = text
}

function close() {
  visible.value = false
  content.value = ''
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog title="查看案例概要" v-model="visible" :close-on-click-modal="false" width="60vw" @close="close">
      <div class="content" v-html="content"></div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.content {
  min-height: 18.75rem;
  line-height: 1.5;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>
