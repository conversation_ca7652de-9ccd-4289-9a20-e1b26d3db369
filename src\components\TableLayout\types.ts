// 表格列配置接口
export interface TableColumn {
  prop?: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  sortable?: boolean
  showOverflowTooltip?: boolean
  slot?: string
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  className?: string
  labelClassName?: string
  [key: string]: any
}

// API 响应数据接口
export interface ApiResponse {
  data: {
    result?: any[]
    list?: any[]
    count?: number
    total?: number
    [key: string]: any
  }
}

// 分页参数接口
export interface PaginationParams {
  pageNum: number
  pageSize: number
  data: Record<string, any>
}

// 表格布局组件 Props 接口
export interface TableLayoutProps {
  // 数据相关
  apiFunction?: (params: any) => Promise<ApiResponse>
  apiParams?: Record<string, any>
  tableData?: any[]

  // 表格配置
  columns: TableColumn[]
  rowKey?: string
  tableProps?: Record<string, any>
  headerCellStyle?: Record<string, any>

  // 分页配置
  showPagination?: boolean
  currentPage?: number
  pageSize?: number
  total?: number
  pageSizes?: number[]
  paginationLayout?: string

  // 操作列配置
  showActionColumn?: boolean
  actionColumnLabel?: string
  actionColumnWidth?: string | number

  // 高度计算配置
  heightOffset?: number
  minHeight?: number

  // 其他配置
  loading?: boolean
  autoLoad?: boolean
  pollInterval?: number
}

// 表格布局组件 Emits 接口
export interface TableLayoutEmits {
  'update:currentPage': [value: number]
  'update:pageSize': [value: number]
  'update:loading': [value: boolean]
  'update:tableData': [value: any[]]
  'update:total': [value: number]
  'data-loaded': [data: any]
  'data-error': [error: any]
}

// 表格布局组件暴露的方法接口
export interface TableLayoutExpose {
  loadData: (resetPage?: boolean) => Promise<void>
  refresh: () => void
  clearSelection: () => void
  tableRef: any
  startPolling: () => void
  stopPolling: () => void
}
