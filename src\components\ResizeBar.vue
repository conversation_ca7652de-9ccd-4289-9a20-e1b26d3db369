<template>
  <div class="resize-bar" @mousedown="onMouseDown">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
// 定义 emits 接口
interface Emits {
  mousedown: [event: MouseEvent]
}

// 定义 emits
const emit = defineEmits<Emits>()

// 鼠标按下事件处理
const onMouseDown = (e: MouseEvent) => {
  emit('mousedown', e)
}
</script>

<style lang="scss" scoped>
.resize-bar {
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background-color: transparent;
  transition: background-color 0.3s;

  &:hover,
  &:active {
    background-color: #e8e8e8;
  }
}
</style>
