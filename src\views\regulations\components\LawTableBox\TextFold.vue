<script setup lang="ts">
const props = defineProps<{ text: string; id: string }>()

function setText(text: string) {
  if (text.length > 30) return text.slice(0, 30) + '...'
  return text
}

function handleMore() {
  console.log(props.id)
}
</script>

<template>
  <div class="text-wrap">
    {{ setText(text) }}
    <el-button class="more-btn" type="primary" link v-if="text.length > 30" @click="handleMore">更多</el-button>
  </div>
</template>

<style lang="scss" scoped>
.text-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  .more-btn {
    position: absolute;
    right: 0;
    bottom: 2px;
  }
}
</style>
