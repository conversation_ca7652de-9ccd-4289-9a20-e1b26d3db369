<template>
  <div class="custom-org-tree-component">
    <div class="component-wrapper">
      <div class="left-tree-wrapper">
        <div class="search-container">
          <el-input
            v-model="matchStr"
            placeholder="搜索"
            clearable
            @input="
              (val) => {
                inputFilter(val)
              }
            "
            @keyup.enter="searchFilter"
          >
            <template v-slot:suffix>
              <i class="el-input__icon el-icon-search search_icon" @click.stop="searchFilter"></i>
            </template>
          </el-input>
        </div>

        <div v-loading="orgTableLoading" class="tree-container">
          <el-tree
            ref="orgUser"
            node-key="nodeId"
            :data="orgTreeList"
            class="org-user-tree-class"
            show-checkbox
            :props="defaultProps"
            empty-text=" "
            :default-checked-keys="defaultCheckedKeys"
            :filter-node-method="filterNode"
            @check="orgNodeCheck"
          >
            <template v-slot="{ data, node }">
              <div class="el-tree-node__label" @click.stop="nodeClick(data, node)">
                <div class="node-content">
                  <div v-if="isShowIcon" class="node-icon">
                    <span v-if="data.isUser" class="el-icon-user"></span>
                    <!-- <img v-else src="@/assets/images/dir.png" /> -->
                  </div>
                  <div class="node-name" :title="data.nodeName">{{ data.nodeName }}</div>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="right-select-wrapper">
        <div class="num-tip">
          <span>已选择{{ rightSelectList.length }}个用户</span>
          <span class="clear" :class="{ 'clear-block': rightSelectList.length > 0 }" @click.stop="clear()">清空</span>
        </div>

        <div class="right-select-list">
          <div class="list-wrapper">
            <div v-for="select in rightSelectList" :key="select.userCode" class="select-node">
              <div class="select-label">
                <span class="black">{{ select.realName }}</span>
                <span class="gray">({{ select.userName }})</span>
              </div>
              <div class="del-btn" @click.stop="cancelRightSelect(select)">
                <svg-icon icon-class="tree-delete" class="tree-delete-icon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { queryOrgPageList, queryAllOrgUser } from '@/services/orgApi'
import { cloneObject } from '@/utils'

// Props
interface Props {
  defaultSelects?: string[]
  isShowIcon?: boolean
}

// User interface
interface UserNode {
  nodeName: string
  userCode: string
  orgIds: string[]
  userId: string
  userName: string
  realName: string
  isUser: boolean
  nodeId?: string
  parnetOrgName?: string
}

const props = withDefaults(defineProps<Props>(), {
  defaultSelects: () => [],
  isShowIcon: true,
})

// Emits
const emit = defineEmits<{
  'init-complete': []
  'select-change': [value: any[]]
  clear: []
}>()

// Reactive data
const matchStr = ref('')
const orgTreeList = ref<any[]>([])
const currentCheckKeys = ref<string[]>([])
const defaultProps = reactive({
  children: 'subList',
  label: 'nodeName',
})
const rightSelectList = ref<UserNode[]>([])
const orgTableLoading = ref(false)
const defaultCheckedKeys = ref<string[]>([])

// Refs
const orgUser = ref()

// Additional reactive data for internal use
const orgUserMap = ref<Record<string, UserNode[]>>({})
const userCodeMap = ref<Record<string, string[]>>({})
const userNodeMap = ref<Record<string, UserNode>>({})
const orgIdMap = ref<Record<string, string[]>>({})
const orgPathMap = ref<Record<string, string>>({})
const timer = ref()

// Lifecycle
onMounted(() => {
  initHandle()
})
// Methods
const disabledProps = () => {
  if (rightSelectList.value.length >= 25) return true
  else return false
}

const nodeClick = (data: any, node: any) => {
  node.expanded = !node.expanded
  if (data.isUser) {
    if (!node.checked) checkUserNode(data.userCode)
    else cancelCheckUserNode(data.userCode)

    updateRightSelect()
  }
}

const initHandle = () => {
  refreshOrgTree()
}

const inputFilter = (val: string) => {
  if (timer.value) clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    searchFilter()
  }, 350)
}

const searchFilter = () => {
  orgUser.value.filter(matchStr.value)
}

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.nodeName.indexOf(value) !== -1 || (data.parnetOrgName || '').indexOf(value) !== -1
}
const refreshOrgTree = async () => {
  orgTableLoading.value = true
  // 获取组织-用户-树
  const userList: any = await getOrgUser()
  const orgIdMapLocal: any = {},
    orgUserMapLocal: any = {},
    userNodeMapLocal: any = {},
    userCodeMapLocal: any = {}
  userList.forEach((user: any) => {
    userCodeMapLocal[user.userCode] = (user.orgIds || []).map((orgId: any) => orgId)

    if (!userNodeMapLocal[user.userCode]) {
      userNodeMapLocal[user.userCode] = user
    }

    if (user.orgIds.length) {
      user.orgIds.forEach((orgId: any) => {
        if (!orgIdMapLocal[orgId]) {
          orgIdMapLocal[orgId] = []
          orgUserMapLocal[orgId] = []
        }
        if (!orgIdMapLocal[orgId].includes(`${user.userCode}`)) {
          orgIdMapLocal[orgId].push(`${user.userCode}`)
          orgUserMapLocal[orgId].push(cloneObject(user))
        }
      })
    }
  })

  orgUserMap.value = orgUserMapLocal
  userCodeMap.value = userCodeMapLocal
  userNodeMap.value = userNodeMapLocal
  orgIdMap.value = orgIdMapLocal

  const result: any = await queryOrgPageList({}).finally(() => {
    orgTableLoading.value = false
  })
  // 组织节点层级 map
  const orgPathMapLocal: any = {},
    checkKeys: any[] = []
  if (result.code == 200) {
    orgTreeList.value = transOrgTree(result.data || [], orgPathMapLocal, checkKeys)

    emit('init-complete')
  }
  defaultCheckedKeys.value = checkKeys
  orgPathMap.value = orgPathMapLocal
  if (checkKeys.length) {
    nextTick(() => {
      currentCheckKeys.value = orgUser.value.getCheckedKeys().slice()
      updateRightSelect()
    })
  }
}
const transOrgTree = (list: any[], orgPathMapLocal: any, checkKeys: any[]) => {
  if (!list.length) return []
  list.forEach((org: any) => {
    if (!org.isUser) {
      org.nodeId = org.id
      org.nodeName = org.orgName
      orgPathMapLocal[org.id] = org.orgIdPath
    }
    if (orgUserMap.value[org.id]) {
      const users = orgUserMap.value[org.id].slice().map((userNode: any) => {
        if (props.defaultSelects.length) {
          if (props.defaultSelects.includes(userNode.userCode)) {
            checkKeys.push(`${org.id}-${userNode.userCode}`)
          }
        }
        return {
          ...userNode,
          nodeId: `${org.id}-${userNode.userCode}`,
          parnetOrgName: org.orgName,
        }
      })
      org.subList = [...users, ...(org.subList || [])]
    }

    if (org.subList && org.subList.length) {
      transOrgTree(org.subList, orgPathMapLocal, checkKeys)
    }
  })
  return list
}

const getOrgUser = () => {
  return new Promise((resolve) => {
    queryAllOrgUser({})
      .then((res: any) => {
        if (res.code == 200) {
          const data = res.data || []
          const arr = data.map((user: any) => {
            return {
              nodeName: user.realName,
              userCode: user.userCode,
              orgIds: (user.orgIdListString || '').split(','),
              userId: user.id,
              userName: user.userName,
              realName: user.realName,
              isUser: true,
            }
          })
          resolve(arr)
        } else {
          resolve([])
        }
      })
      .catch(() => {
        resolve([])
      })
  })
}
const orgNodeCheck = (data: any, checked: any) => {
  // 勾选触发
  const { checkedKeys } = checked
  const isCheck = checkedKeys.includes(data.nodeId)
  if (data.isUser) {
    if (isCheck) {
      checkUserNode(data.userCode)
    } else {
      cancelCheckUserNode(data.userCode)
    }
  } else {
    if (isCheck) {
      checkOrgNode(data.id)
    } else {
      cancelCheckOrgNode(data.id)
    }
  }
  updateRightSelect()
}

const updateRightSelect = () => {
  // 更新右边的勾选用户
  const cacheMap: any = {},
    checkUserKey: any[] = []
  currentCheckKeys.value.slice().forEach((key: string) => {
    key.split('-').forEach((k: string) => {
      if (userNodeMap.value[k] && !cacheMap[k]) {
        cacheMap[k] = 1
        checkUserKey.push(k)
      }
    })
  })
  rightSelectList.value = checkUserKey.map((userCode: string) => {
    return userNodeMap.value[userCode]
  })

  emit('select-change', rightSelectList.value.slice())
}
const cancelCheckUserNode = (code: string) => {
  // 取消勾选用户
  const oldkeys = orgUser.value.getCheckedKeys().slice()

  const orgKeys: any[] = []
  const userKeys = (userCodeMap.value[code] || []).map((orgId: any) => {
    // orgKeys.push(orgId);
    orgPathMap.value[orgId].split(',').forEach((pathId: any) => {
      if (!orgKeys.includes(pathId)) {
        if (pathId != 0) orgKeys.push(pathId)
      }
    })
    return `${orgId}-${code}`
  })

  const newKeys = oldkeys.filter((key: string) => {
    if (userKeys.includes(key) || orgKeys.includes(key)) return false
    else return true
  })
  currentCheckKeys.value = newKeys

  nextTick(() => {
    orgUser.value.setCheckedKeys(newKeys)
  })
}

const checkUserNode = (code: string) => {
  // 勾选用户
  const oldkeys = orgUser.value.getCheckedKeys().slice()

  const userKeys = (userCodeMap.value[code] || []).map((orgId: any) => {
    return `${orgId}-${code}`
  })

  const obj: any = {}
  oldkeys.concat(userKeys).forEach((k: string) => {
    if (!obj[k]) obj[k] = 1
  })
  const newKeys = Object.keys(obj)

  currentCheckKeys.value = newKeys

  nextTick(() => {
    orgUser.value.setCheckedKeys(newKeys)
  })
}
const cancelCheckOrgNode = (id: string) => {
  // 取消勾选组织
  const oldkeys = orgUser.value.getCheckedKeys().slice()

  // 需要取消勾选的用户和组织节点
  const userKeys: any[] = [],
    orgKeys: any[] = []
  ;(orgIdMap.value[id] || []).forEach((userCode: string) => {
    ;(userCodeMap.value[userCode] || []).forEach((orgId: any) => {
      if (!orgKeys.includes(orgId)) orgKeys.push(orgId)
      userKeys.push(`${orgId}-${userCode}`)
    })
  })
  const newKeys = oldkeys.filter((key: string) => {
    if (userKeys.includes(key) || orgKeys.includes(key)) return false
    else return true
  })
  currentCheckKeys.value = newKeys

  nextTick(() => {
    orgUser.value.setCheckedKeys(newKeys)
  })
}

const checkOrgNode = (id: string) => {
  // 勾选组织
  const oldkeys = orgUser.value.getCheckedKeys().slice()
  const userKeys: any[] = []
  ;(orgIdMap.value[id] || []).forEach((userCode: string) => {
    ;(userCodeMap.value[userCode] || []).forEach((orgId: any) => {
      userKeys.push(`${orgId}-${userCode}`)
    })
  })

  const obj: any = {}
  oldkeys.concat(userKeys).forEach((k: string) => {
    if (!obj[k]) obj[k] = 1
  })
  const newKeys = Object.keys(obj)

  currentCheckKeys.value = newKeys

  nextTick(() => {
    orgUser.value.setCheckedKeys(newKeys)
  })
}

const cancelRightSelect = (user: any) => {
  // 右侧勾选 取消用户
  cancelCheckUserNode(user.userCode)
  updateRightSelect()
}
const setCheckUserList = (userCodeList: string[]) => {
  if (!(userCodeList instanceof Array)) {
    console.error('function setCheckUserList need userCode Array')
    return false
  }
  // 直接调用 userCode list
  const oldkeys: any[] = []

  const cacheMap: any = {}
  let userKeys: any[] = []
  userCodeList.forEach((userCode: string) => {
    if (!cacheMap[userCode]) {
      cacheMap[userCode] = 1
      const keys = (userCodeMap.value[userCode] || []).map((orgId: any) => {
        return `${orgId}-${userCode}`
      })

      userKeys = userKeys.concat(keys)
    }
  })

  const obj: any = {}
  oldkeys.concat(userKeys).forEach((k: string) => {
    if (!obj[k]) obj[k] = 1
  })
  const newKeys = Object.keys(obj)

  currentCheckKeys.value = newKeys

  nextTick(() => {
    orgUser.value.setCheckedKeys(newKeys)
    updateRightSelect()
  })
}

const clear = () => {
  rightSelectList.value = []
  orgUser.value.setCheckedKeys([])
  emit('clear')
}

// Expose methods for parent component access
defineExpose({
  rightSelectList,
  setCheckUserList,
  clear,
})
</script>
<style lang="scss" scoped>
.custom-org-tree-component {
  width: 100%;
  height: 100%;

  .component-wrapper {
    display: flex;
    height: 100%;
  }

  .left-tree-wrapper {
    flex: 1;
    height: 100%;
    border-right: 1px solid #d4d4d4;

    .search-container {
      padding-right: 16px;
    }

    .tree-container {
      position: relative;
      height: calc(100% - 35px);
      padding-top: 10px;
      padding-right: 12px;
      overflow-y: auto;
    }
  }
}

.right-select-wrapper {
  flex: 1;
  height: 100%;

  .num-tip {
    display: flex;
    justify-content: space-between;
    padding-left: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    color: #595959;

    .clear {
      display: none;
      color: var(--main-bg);
      cursor: pointer;
    }

    .clear-block {
      display: block;
    }
  }

  .right-select-list {
    position: relative;
    height: calc(100% - 20px);
    padding-top: 10px;
    padding-bottom: 5px;

    .list-wrapper {
      height: 100%;
      overflow-y: auto;
    }

    .select-node {
      position: relative;
      display: flex;
      align-items: center;
      height: 32px;
      padding-left: 16px;
      cursor: pointer;

      &:hover {
        background: #f5f7fa;
      }

      .select-label {
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap;
      }

      .black {
        color: #262626;
      }

      .gray {
        color: #929292;
      }
    }

    .del-btn {
      position: absolute;
      top: 8px;
      right: 0;
      width: 16px;
      height: 16px;
      font-size: 14px;

      &:hover {
        opacity: 0.75;
      }
    }
  }
}

.org-user-tree-class {
  .node-content {
    position: relative;
    display: flex;
    max-width: 270px;
    white-space: pre-wrap;
  }

  .node-name {
    margin-left: 1px;
    color: #262626;

    &.primary {
      color: var(--main-bg);
    }
  }

  .node-icon {
    margin-right: 5px;

    img {
      max-width: 16px;
      max-height: 16px;
    }
  }

  :deep(.el-tree-node__expand-icon) {
    color: #929292;

    &.is-leaf {
      color: transparent;
    }
  }

  :deep(.el-tree-node__content) {
    position: relative;
    height: 34px;
  }

  .el-tree-node__label {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  :deep(.el-tree-node__children) {
    .el-tree-node__label {
      color: var(--main-bg) !important;

      &:hover {
        opacity: 0.7;
      }
    }
  }
}
</style>
