import { getTagsByBizCode } from '@/services/judicialCase'
import type { TagItem } from './Model'

export function useTagService() {
  const tagsData = ref<TagItem[]>([])

  const loading = ref(false)

  /**
   * 标签业务类型
   */
  enum TagBizType {
    CASE = 1, //司法案例
    LAW = 2, //法律法规
    LAW_CLAUSE = 4, //法律法规条款
    CONTRACT = 3, //合同范本
    NORMAL = 0, //通用业务
  }
  const queryTagsList = async () => {
    try {
      loading.value = true
      const { code, data } = await getTagsByBizCode(String(TagBizType.CASE))
      tagsData.value = data as TagItem[]
      loading.value = false
    } catch (e) {
      console.error(e)
    } finally {
      loading.value = false
    }
  }
  onUnmounted(() => {
    loading.value = false
    tagsData.value = []
  })
  return { tagsData, loading, queryTagsList }
}
