import request from '@/services'
import type { IResponse } from '@/services'

// 获取用户列表分页信息
export interface IUserQueryData {
  userName?: string // 用户账号
  userStatus?: number
  realName?: string // 用户名称
}
// 请求参数
interface IQueryPageUserListParams {
  pageNum: number
  pageSize: number
  data?: IUserQueryData
}
// 响应参数
export interface IUser {
  id: string
  userStatus: number
  realName: string
  userName: string // 用户账号
  createTime: string
  updateTime: string
  createBy: string
  updateBy: string
  versionNumber: number
  // 密码字段 ////////
}
export interface IQueryPageUserList {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: IUser[]
}
// 接口
export function queryPageUserList<T>(data: IQueryPageUserListParams): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/page`,
    method: 'post',
    data,
  })
}

// 更新用户状态
// 请求参数
export interface IUpdateUserStatusParams {
  id: string
  userStatus: number
}
// 接口
export function updateUserStatus<T>(data: IUpdateUserStatusParams): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/updateStatus`,
    method: 'post',
    data,
  })
}

// 新增用户
export interface IAddUserInfoParams {
  userName: string // 用户账号
  realName: string
  userStatus: number
}
export function addUserInfo<T>(data: IAddUserInfoParams): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/add`,
    method: 'post',
    data,
  })
}

// 编辑用户
export interface IEditUserInfoParams {
  id: string
  realName: string
  userStatus: number
}
export function editUserInfo<T>(data: IEditUserInfoParams): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/edit`,
    method: 'post',
    data,
  })
}

// 重置密码
export function resetPassword<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/resetPwd/${id}`,
    method: 'post',
  })
}

// 删除
export function deleteUser<T>(id: string): Promise<IResponse<T>> {
  return request({
    url: `/sysUser/delete/${id}`,
    method: 'post',
  })
}
