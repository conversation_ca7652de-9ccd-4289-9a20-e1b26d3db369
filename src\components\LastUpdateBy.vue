<template>
  <el-select
    v-model="selectedValue"
    filterable
    clearable
    remote
    reserve-keyword
    :placeholder="props.placeholder"
    :remote-method="remoteQuery"
    :loading="remoteLoading"
    @change="changeData"
    @focus="focusQuery"
  >
    <el-option v-for="item in lastUpdatedByList" :key="item.userCode" :label="item.realName" :value="item.userCode">
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { searchLastUpdatedBy } from '../services/ruleBaseApi'

// 定义 props
interface Props {
  lastUpdatedBy?: string
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  lastUpdatedBy: '',
  placeholder: '更新人',
})

// 定义 emits
const emit = defineEmits<{
  'update:lastUpdatedBy': [value: string]
  'update:last-updated-by': [value: string]
}>()

// 响应式数据
const remoteLoading = ref(false)
const lastUpdatedByList = ref<Array<{ userCode: string; realName: string }>>([])
const selectedUser = ref<{ userCode: string; realName: string } | null>(null)
const selectedValue = ref<string>('')

// 方法
const changeData = (e: string) => {
  selectedValue.value = e || ''
  if (e) {
    // 找到选中的用户信息并保存
    const user = lastUpdatedByList.value.find((item) => item.userCode === e)
    if (user) {
      selectedUser.value = user
    }
  } else {
    // 如果清空选择，也清空选中用户
    selectedUser.value = null
  }
  emit('update:lastUpdatedBy', e)
  // emit('update:last-updated-by', e)
}

const remoteQuery = async (query: string) => {
  if (query !== '') {
    remoteLoading.value = true
    try {
      const { data, code } = await searchLastUpdatedBy({
        realName: query,
      })
      if (code == 200) {
        // 确保选中的用户始终在列表中
        const searchResults: Array<{ userCode: string; realName: string }> = data || []
        if (selectedUser.value) {
          const isSelectedInResults = searchResults.some(
            (item: { userCode: string; realName: string }) => item.userCode === selectedUser.value?.userCode,
          )
          if (!isSelectedInResults) {
            lastUpdatedByList.value = [selectedUser.value, ...searchResults]
          } else {
            lastUpdatedByList.value = searchResults
          }
        } else {
          lastUpdatedByList.value = searchResults
        }
      }
    } finally {
      remoteLoading.value = false
    }
  } else {
    // 当查询为空时，如果有选中的用户，保留在列表中以便显示
    if (selectedUser.value) {
      lastUpdatedByList.value = [selectedUser.value]
    } else {
      lastUpdatedByList.value = []
    }
  }
}

const focusQuery = (e: Event) => {
  const target = e.target as HTMLInputElement
  if (!target.value) {
    // 如果没有输入值但有选中的用户，保留选中的用户在列表中
    if (selectedUser.value) {
      lastUpdatedByList.value = [selectedUser.value]
    } else {
      lastUpdatedByList.value = []
    }
  }
}

// 监听 props.lastUpdatedBy 的变化，初始化选中用户
watch(
  () => props.lastUpdatedBy,
  async (newValue) => {
    selectedValue.value = newValue || ''
    if (newValue && (!selectedUser.value || selectedUser.value.userCode !== newValue)) {
      // 如果有初始值但没有选中用户信息，或者值发生了变化
      // 尝试通过搜索API获取用户信息，如果失败则使用用户代码作为显示名称
      try {
        const { data, code } = await searchLastUpdatedBy({
          realName: newValue,
        })
        if (code == 200 && data && data.length > 0) {
          // 找到匹配的用户
          const matchedUser = data.find((user: { userCode: string; realName: string }) => user.userCode === newValue)
          if (matchedUser) {
            selectedUser.value = matchedUser
          } else {
            // 如果没找到匹配的，使用第一个结果或者创建一个占位符
            selectedUser.value = { userCode: newValue, realName: newValue }
          }
        } else {
          // API 调用失败或没有结果，使用用户代码作为显示名称
          selectedUser.value = { userCode: newValue, realName: newValue }
        }
      } catch (error) {
        // 搜索失败，使用用户代码作为显示名称
        selectedUser.value = { userCode: newValue, realName: newValue }
      }
      if (selectedUser.value) {
        lastUpdatedByList.value = [selectedUser.value]
      }
    } else if (!newValue) {
      // 如果值被清空，也清空选中用户
      selectedUser.value = null
      lastUpdatedByList.value = []
    }
  },
  { immediate: true },
)
</script>
