<script setup lang="ts">
withDefaults(defineProps<{ visible: boolean; title: string }>(), {
  visible: false,
})

const emits = defineEmits(['update:visible'])
function goBack() {
  emits('update:visible', false)
}
</script>
<template>
  <Teleport defer to="#app-main">
    <Transition>
      <div class="teleport-wrap" v-if="visible">
        <div class="title-wrap">
          <span @click="goBack">返回</span><span class="title-name">{{ title }}</span>
        </div>
        <slot></slot>
      </div>
    </Transition>
  </Teleport>
</template>

<style lang="scss" scoped>
.teleport-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  background-color: #fff;
  .title-wrap {
    height: 56px;
    padding-left: 16px;
    line-height: 56px;
    border-bottom: 1px solid var(--header-bottom-line);
    .title-name {
      margin-left: 12px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
