<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    :title="'新增' + name"
    append-to-body
    :close-on-click-modal="false"
    width="480px"
    top="360px"
    show-close
    @close="close"
  >
    <el-form
      ref="refForm"
      label-width="110px"
      :model="ruleForm"
      :rules="rules"
      @submit.prevent
      @keyup.enter="handleCommit"
    >
      <el-form-item :label="name + '名称'" prop="name">
        <el-input v-model="ruleForm.name" clearable :placeholder="'请输入' + name" />
      </el-form-item>
      <div class="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleCommit">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, type FormInstance } from 'element-plus'
import { createRuleList } from '@/services/ruleBaseApi'

// 定义 emits
const emit = defineEmits<{
  handleReviewItem: [item: any, obj: any]
}>()

// 定义接口
interface TreeNode {
  id: string
  label: string
  children?: TreeNode[]
  unfold?: boolean
  isEdit?: boolean
  isActive?: boolean
  isHide?: boolean
  ruleItem?: any[]
}

interface RuleForm {
  name: string
}

// 响应式数据
const route = useRoute()
const ruleListId = route.query.id as string
const name = ref('')
const rules = reactive({
  name: [{ required: true, message: `请输入${name.value}`, trigger: 'blur' }],
})

const visible = ref(false)
const ruleForm = ref<RuleForm>({ name: '' })
const loading = ref(false)
const refForm = ref<FormInstance>()
let node: TreeNode | TreeNode[] | null = null

// 方法
async function open(val: TreeNode | TreeNode[]) {
  node = val
  name.value = Array.isArray(val) ? '审查类型' : '审查项'
  visible.value = true
}

function close() {
  refForm.value?.resetFields()
  nextTick(() => {
    refForm.value?.clearValidate()
    visible.value = false
  })
}

function handleCommit() {
  refForm.value?.validate(async (valid, fields) => {
    if (valid) {
      const params = {
        parentId: (node as TreeNode).id,
        ruleListId,
        ruleTypeName: ruleForm.value.name,
      }
      try {
        loading.value = true
        const { data } = await createRuleList(params)
        ElMessage.success('创建成功！')

        const obj = {
          label: ruleForm.value.name,
          id: data,
          isEdit: false,
          isActive: false,
        }

        if ((node as TreeNode).children) {
          const nodeObj = node as TreeNode
          Object.assign(obj, { isHide: false, ruleItem: [] })
          nodeObj.unfold = true
          nodeObj.children!.push(obj)
          emit('handleReviewItem', null, obj)
        } else {
          Object.assign(obj, { children: [] })
          ;(node as TreeNode[]).push({ ...obj, unfold: false })
        }
      } catch (error) {
        console.error('创建失败:', error)
      } finally {
        loading.value = false
      }
      close()
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 暴露方法给父组件
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.input {
  width: 362px;
  margin-left: 38px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-top: 40px;
}

:deep(.el-dialog__body) {
  padding-bottom: 12px;
}
</style>
