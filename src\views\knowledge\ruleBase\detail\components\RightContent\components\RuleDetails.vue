<template>
  <div class="page-inner">
    <slot name="header"></slot>
    <template v-if="ruleObj">
      <div class="page-content">
        <div class="content-top">
          <el-row>
            <el-form ref="refBase" :model="ruleObj" :rules="rules">
              <el-col :span="24">
                <el-form-item label="风险标签" prop="ruleName" class="content-all">
                  <el-input v-model.trim="ruleObj.ruleName" placeholder="请输入风险标签" :maxlength="100"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="信息抽取(prompts）" prop="infoExtractPrompts" class="content-all">
                  <el-input
                    v-model.trim="ruleObj.infoExtractPrompts"
                    placeholder="请输入信息抽取"
                    :maxlength="2000"
                    :rows="4"
                    type="textarea"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="风险判断(prompts）" prop="riskJudgePrompts" class="content-all">
                  <el-input
                    v-model.trim="ruleObj.riskJudgePrompts"
                    placeholder="请输入风险提示"
                    :maxlength="2000"
                    :rows="4"
                    type="textarea"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="风险等级" class="content-all" required>
                  <el-select v-model="ruleObj.ruleLevel" placeholder="请选择">
                    <el-option
                      v-for="item in levelOption"
                      :key="item.code"
                      :label="item.label"
                      :value="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="修改建议" prop="ruleSuggestion" class="content-all">
                  <el-input
                    v-model.trim="ruleObj.ruleSuggestion"
                    placeholder="请输入修改建议"
                    :maxlength="1000"
                    :rows="4"
                    type="textarea"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <div v-for="(item, idx) in contentOptions" :key="idx" class="content-item">
          <span>{{ item.label }}</span>
          <el-input v-model.trim="ruleObj[item.code]" type="textarea"></el-input>
        </div>
      </div>
      <slot name="footer"></slot>
    </template>
    <div v-else class="empty-warp">
      <!-- TODO -->
      <!-- <img src="@/assets/images/empty.png" alt="" /> -->
      <span>请先添加自定义规则标签</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { queryRuleDetail } from '@/services/ruleBaseApi'

// 定义 slots
defineSlots<{
  header?: any
  footer?: any
}>()

// 定义 emits
const emit = defineEmits<{
  save: [ruleObj: RuleObj, ruleNode: RuleNode]
}>()

// 定义接口
interface RuleNode {
  id: string
  ruleItem?: RuleObj
}

interface RuleObj {
  ruleTypeId: string
  infoExtractPrompts: string
  riskJudgePrompts: string
  ruleLevel: number
  ruleName: string
  ruleSuggestion: string
  ruleReference: string
  ruleRisk: string
  lawAccording: string
  reviewBasis: string
}

interface LevelOption {
  code: number
  label: string
}

interface ContentOption {
  code: keyof RuleObj
  label: string
}

// 初始数据
const initData: RuleObj = {
  ruleTypeId: '',
  infoExtractPrompts: '',
  riskJudgePrompts: '',
  ruleLevel: 3,
  ruleName: '',
  ruleSuggestion: '',
  ruleReference: '',
  ruleRisk: '',
  lawAccording: '',
  reviewBasis: '',
}

// 响应式数据
const ruleObj = ref<RuleObj | null>(null)
const refBase = ref<FormInstance>()
let ruleNode: RuleNode | null = null

const levelOption = ref<LevelOption[]>([
  { code: 1, label: '高' },
  { code: 2, label: '中' },
  { code: 3, label: '低' },
])

const rules = ref<FormRules<RuleObj>>({
  ruleName: [{ required: true, message: '请输入风险标签', trigger: 'blur' }],
  infoExtractPrompts: [{ required: true, message: '请输入信息抽取', trigger: 'blur' }],
  riskJudgePrompts: [{ required: true, message: '请输入风险判断', trigger: 'blur' }],
})

const contentOptions: ContentOption[] = [
  { code: 'ruleRisk', label: '风险说明' },
  { code: 'ruleReference', label: '参考条款' },
  { code: 'lawAccording', label: '法律依据' },
  { code: 'reviewBasis', label: '审查依据' },
]

// 方法
async function getRuleData(node: RuleNode | null) {
  refBase.value?.clearValidate()
  if (!node) {
    ruleObj.value = null
    return
  }
  ruleNode = node
  const { data } = await queryRuleDetail({ id: node.id })
  if (!data) {
    ruleObj.value = JSON.parse(JSON.stringify(initData))
  } else {
    ruleObj.value = data
  }
}

function handleSaveData() {
  refBase.value?.validate((valid) => {
    if (valid && ruleObj.value && ruleNode) {
      emit('save', ruleObj.value, ruleNode)
    }
  })
}

const renderRuleDetails = (item: { ruleItem: RuleObj }) => {
  ruleObj.value = item.ruleItem
}

// 暴露方法给父组件
defineExpose({
  getRuleData,
  handleSaveData,
  renderRuleDetails,
})
</script>

<style lang="scss" scoped>
.page-inner {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;

  .page-content {
    flex: 1;
    overflow-y: auto;
  }
}

.content-top {
  :deep(.el-form) {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;

    .el-form-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 0;

      .el-form-item__label {
        display: block;
        width: 100%;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        color: #262626;
        text-align: left;
      }

      .el-form-item__content {
        width: 100%;

        .el-input,
        .el-select,
        .el-textarea {
          width: 100% !important;
        }
      }
    }
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input) {
    width: 100%;
  }

  .content-all {
    width: 100%;
  }
}

.content-item {
  display: flex;
  flex-direction: column;
  margin-top: 24px;

  :deep(.el-textarea__inner) {
    height: 64px;
  }

  span {
    padding-bottom: 12px;
  }
}

:deep(.el-form-item) {
  width: 100% !important;
}

.empty-warp {
  position: absolute;
  top: 60px;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100% - 60px);

  span {
    margin-top: 16px;
    color: #929292;
  }
}
</style>
