<script setup lang="ts">
const props = defineProps({
  resultData: {
    type: Array,
  },
  count: {
    type: Number,
    default: 0,
  },
})
</script>
<template>
  <div class="error-wrap">
    <div class="item">导入结果</div>
    <div class="desc">
      成功导入文件：<span>{{ count }}个</span>
    </div>
    <div v-if="resultData?.length" class="desc">导入失败文件列表</div>
    <el-table
      v-if="resultData?.length"
      ref="table"
      height="100%"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="resultData"
    >
      <el-table-column label="文件名称" :show-overflow-tooltip="true" min-width="200">
        <template v-slot="scope">
          <span>{{ scope.row.fileName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="失败原因">
        <template v-slot="scope">
          <span>{{ scope.row.importResults || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.error-wrap {
  .item {
    display: flex;
    align-items: center;
    height: 1.5rem;
    padding-left: 0.5rem;
    border-left: 2px solid var(--main-bg);
  }
  .desc {
    display: flex;
    align-items: center;
    height: 1.5rem;
    padding-left: 10px;
  }
}
</style>
