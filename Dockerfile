FROM hub.fabigbig.com/innovation/node:20.18-bullseye-slim as build-stage

WORKDIR /app

RUN npm install -g pnpm --registry https://registry.npmmirror.com/

COPY package.json pnpm-lock.yaml ./

RUN pnpm install --registry https://registry.npmmirror.com/

COPY . .

# RUN npm run build:pnpm
RUN npm run build:test

FROM hub.fabigbig.com/innovation/nginx:basic

ENV API_HOST=base-iterms-saas-gateway

COPY --from=build-stage /app/dist/  /www/html/

COPY --from=build-stage /app/nginx/default.conf.template  /etc/nginx/templates/

# COPY --from=build-stage /app/nginx/nginx.conf  /etc/nginx/

COPY --from=build-stage /app/nginx/mime.types  /etc/nginx/

CMD ["nginx", "-g", "daemon off;"]
