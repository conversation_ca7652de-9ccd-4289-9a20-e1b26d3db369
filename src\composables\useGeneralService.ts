import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { getShareScope, getTagsByBizCode } from '@/services/judicialCase'

export interface ITagItem {
  id: string
  labelName: string
  bizType: string
  bizTypeName: string
  enableStatus: boolean
  corpName: string
}
export interface IShareScope {
  value: number
  name: string
}

export function useGeneralService() {
  const tagsData = ref<ITagItem[]>([])
  const scopeData = ref<IShareScope[]>([])
  const loading = ref(false)

  /**
   * 标签业务类型
   */
  enum TagBizType {
    CASE = 1, //司法案例
    LAW = 2, //法律法规
    LAW_CLAUSE = 4, //法律法规条款
    CONTRACT = 3, //合同范本
    NORMAL = 0, //通用业务
  }
  const queryTagsList = async (tagBizType: string) => {
    try {
      loading.value = true
      const { code, data, message } = await getTagsByBizCode(tagBizType)
      if (code === RESPONSE_CODE_SUCCESS) {
        tagsData.value = data as ITagItem[]
      } else {
        ElMessage.error(message)
      }
      loading.value = false
    } catch (e) {
      console.error(e)
    } finally {
      loading.value = false
    }
  }
  onUnmounted(() => {
    loading.value = false
    tagsData.value = []
    scopeData.value = []
  })

  const loadShareScope = async () => {
    const { code, data } = await getShareScope()
    if (code === RESPONSE_CODE_SUCCESS) {
      scopeData.value = data as IShareScope[]
    }
  }
  return { tagsData, loading, scopeData, queryTagsList, loadShareScope, TagBizType }
}
