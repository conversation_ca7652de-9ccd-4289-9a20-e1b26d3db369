<template>
  <div class="table-layout-wrap">
    <!-- 表格主体区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        :row-key="rowKey"
        :height="tableHeight"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        v-bind="tableProps"
      >
        <!-- 动态渲染列 -->
        <el-table-column v-for="column in columns" :key="column.prop || column.label" v-bind="column">
          <template v-if="column.slot" #default="scope">
            <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index" />
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column v-if="showActionColumn" fixed="right" :label="actionColumnLabel" :width="actionColumnWidth">
          <template #default="scope">
            <slot name="actions" :row="scope.row" :index="scope.$index" />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-pagination
        v-if="showPagination"
        v-model:current-page="internalCurrentPage"
        v-model:page-size="internalPageSize"
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        :total="internalTotal"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <div v-else class="table-total">共 {{ tableData?.length }} 条</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import useWindowResize from '@/composables/useWindowResize'
import type { TableLayoutProps, TableLayoutEmits, TableLayoutExpose, PaginationParams, ApiResponse } from './types'

// Props 定义
const props = withDefaults(defineProps<TableLayoutProps>(), {
  rowKey: 'id',
  showPagination: true,
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: () => [10, 20, 30, 40],
  paginationLayout: 'total, prev, pager, next, sizes, jumper',
  showActionColumn: true,
  actionColumnLabel: '操作',
  actionColumnWidth: 200,
  heightOffset: 234, // 默认高度偏移量：搜索框(100) + 按钮(60) + 分页器(50) + 边距(24)
  minHeight: 300,
  loading: false,
  autoLoad: true,
  pollInterval: 0,
  headerCellStyle: () => ({ 'padding-left': '4px' }),
  tableProps: () => ({}),
  apiParams: () => ({}),
})

// Emits 定义
const emit = defineEmits<TableLayoutEmits>()

// 响应式数据
const tableRef = ref()
const { width, height } = useWindowResize()
const internalTableData = ref<any[]>([])
const internalLoading = ref(false)
const internalCurrentPage = ref(1)
const internalPageSize = ref(10)
const internalTotal = ref(0)
let pollTimer: number | null = null

// 计算属性
const tableData = computed(() => props.tableData || internalTableData.value)
const loading = computed({
  get: () => props.loading || internalLoading.value,
  set: (value) => {
    internalLoading.value = value
    emit('update:loading', value)
  },
})

// 初始化内部状态
onMounted(() => {
  // 如果提供了外部值，则使用外部值初始化内部状态
  if (props.currentPage) {
    internalCurrentPage.value = props.currentPage
  }

  if (props.pageSize) {
    internalPageSize.value = props.pageSize
  }

  if (props.total) {
    internalTotal.value = props.total
  }
})

// 计算表格高度
const tableHeight = computed(() => {
  const pageContainerHeight = height.value - 56 // 减去头部导航
  const availableHeight = pageContainerHeight - props.heightOffset
  return Math.max(availableHeight, props.minHeight)
})

// 监听窗口大小变化，重新布局表格
watch([width, height], () => {
  if (tableRef.value) {
    tableRef.value.doLayout()
  }
})

// 数据加载方法
const loadData = async (resetPage = false) => {
  if (!props.apiFunction) return

  if (resetPage) {
    console.log('重置页面到第一页')
    internalCurrentPage.value = 1
    emit('update:currentPage', 1)
  }

  // 确保深拷贝 apiParams，避免引用问题
  const apiParamsClone = JSON.parse(JSON.stringify(props.apiParams || {}))

  const params: PaginationParams = {
    pageNum: internalCurrentPage.value,
    pageSize: internalPageSize.value,
    // data: apiParamsClone,
    ...apiParamsClone,
  }

  loading.value = true

  try {
    const response = await props.apiFunction(params)
    console.log('TableLayout loadData response:', response)

    console.log('TableLayout loadData response.data:', response.data)
    const { data } = response as ApiResponse

    if (props.tableData === undefined) {
      internalTableData.value = data.result || data.results || data.list || []
      emit('update:tableData', internalTableData.value)
    }

    internalTotal.value = +(data.count || data.total || 0)
    emit('update:total', internalTotal.value)

    // 重新布局表格
    if (tableRef.value) {
      tableRef.value.doLayout()
    }

    emit('data-loaded', data)
  } catch (error) {
    console.error('Table data loading failed:', error)

    // 接口报错时停止轮询
    if (props.pollInterval > 0) {
      console.log('接口报错，停止轮询')
      stopPolling()
    }

    emit('data-error', error)
  } finally {
    loading.value = false
  }
}

// 分页处理方法
const handleCurrentChange = (page: number) => {
  internalCurrentPage.value = page
  emit('update:currentPage', page)
  loadData()
}

const handleSizeChange = (size: number) => {
  internalPageSize.value = size
  internalCurrentPage.value = 1
  emit('update:pageSize', size)
  emit('update:currentPage', 1)
  loadData()
}

// 刷新数据
const refresh = () => {
  loadData(true)
}

// 清除选择
const clearSelection = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 轮询设置
const startPolling = () => {
  if (props.pollInterval > 0) {
    pollTimer = setInterval(() => {
      loadData()
    }, props.pollInterval)
  }
}

const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 暴露方法给父组件
defineExpose<TableLayoutExpose>({
  loadData,
  refresh,
  clearSelection,
  tableRef,
  startPolling,
  stopPolling,
})

// 生命周期
onMounted(() => {
  if (props.autoLoad) {
    loadData()
  }
  startPolling()
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss" scoped>
.table-layout-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 56px);
  padding: 0 24px;
  overflow: hidden;
  background-color: var(--bg-color);
  .table-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    :deep(.el-table) {
      font-size: 14px;
      color: var(--font-color);
    }
    :deep(.el-table__fixed::before),
    :deep(.el-table__fixed-right::before) {
      background-color: transparent;
    }
    :deep(.el-table__body-wrapper) {
      overflow: auto;
    }
    :deep(.el-table__header-wrapper) {
      overflow: visible;
    }
    :deep(.el-table__body-wrapper::-webkit-scrollbar) {
      width: 8px;
      height: 8px;
    }
    :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
      background-color: #c1c1c1;
      border-radius: 4px;
    }
    :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
      background-color: #f1f1f1;
      border-radius: 4px;
    }
    :deep(.el-pagination) {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;
      margin-top: 8px;
    }
    .table-total {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;
      margin-top: 1rem;
      margin-right: 20px;
      font-size: 1rem;
      color: #606266;
    }
  }
}
</style>
