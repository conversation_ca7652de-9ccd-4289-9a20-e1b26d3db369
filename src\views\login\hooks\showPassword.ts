import { ref, computed } from 'vue'

export default function showPassword() {
  const passwd = ref('password')
  const passIcon = computed(() => {
    return passwd.value === 'text' ? 'icon-nexus-zhengyan' : 'icon-nexus-biyan'
  })

  function showPass() {
    if (passwd.value === 'text') {
      passwd.value = 'password'
    } else {
      passwd.value = 'text'
    }
  }

  return {
    passwd,
    passIcon,
    showPass,
  }
}
