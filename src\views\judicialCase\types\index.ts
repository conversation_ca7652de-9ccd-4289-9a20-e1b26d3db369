// enum sourceEnum {
//   MANUAL = 0, // 排队中
//   AUTO = 1,
// }

// enum lowEnum {
//   LAW = 1,
//   ADMIN = 2,
//   DEPART = 3,
//   JUDICAL =4
//   PLACE = 5
// }

// enum timelinessEnum = {
//   NOW = 1
// }

export interface ILawCaseSearchArgs {
  caseNumber?: string
  caseName?: string
  caseReason?: string
  courtName?: string
  judge?: string[]
  status?: string
  scope?: string //可见范围
  source?: string
  creater?: string
  labelIds?: string[]
  judgeStartDate?: string
  judgeEndDate?: string
}

export interface IParams<T> {
  pageNum: number
  pageSize: number
  data: Partial<T>
}

export interface ILawRecord {
  id: string
  lawName: string
  lawType: string
}

export interface ILawResponse {
  count: number
  list: ILawRecord[]
}

export interface ILawTableParams {
  page: number
  pageSize: number
  data: { content?: string }
}

export interface ILawTableRecord {
  lawName: string
  book: string
  chapter: string
  festival: string
  terms: string
  tag: string
}

export interface ILawTableResponse {
  count: number
  result: ILawTableRecord[]
}

export interface IProps<T> {
  searchArgs: Partial<T>
  isSearchBxExpand: boolean
}

export interface IResponse<T> {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: T[]
}

export interface ILawCase {
  createBy: string
  updateBy: string
  createTime: string
  updateTime: string
  corpId: string
  createByName: string
  updateByName: string
  corpName: string
  id: string
  caseNumber: string
  caseName: string
  caseSummary: string
  casePoint: string
  caseReason: string
  caseEvaluate: string
  caseLocation: string
  lawSuit: string
  courtName: string
  courtViewPoint: string
  courtEvidence: string
  judgeResults: string
  judgePoint: string
  judgeTime: string
  judgeTimeStr: string
  labelIds: string[]
  scope: string
}

export interface IOption {
  value: string
  label: string
}
