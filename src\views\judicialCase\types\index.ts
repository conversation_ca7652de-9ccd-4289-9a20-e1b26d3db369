// enum sourceEnum {
//   MANUAL = 0, // 排队中
//   AUTO = 1,
// }

// enum lowEnum {
//   LAW = 1,
//   ADMIN = 2,
//   DEPART = 3,
//   JUDICAL =4
//   PLACE = 5
// }

// enum timelinessEnum = {
//   NOW = 1
// }

export interface ISearchArgs {
  lawName: string
  lawType: string
  timeliness: string
  tag: string
  status: string
  scope: string
  source: string
  creater: string
}

export interface ILawParams {
  page: number
  pageSize: number
  data: Partial<ISearchArgs>
}

export interface ILawRecord {
  id: string
  lawName: string
  lawType: string
}

export interface ILawResponse {
  count: number
  result: ILawRecord[]
}

export interface ILawTableParams {
  page: number
  pageSize: number
  data: { content?: string }
}

export interface ILawTableRecord {
  lawName: string
  book: string
  chapter: string
  festival: string
  terms: string
  tag: string
}

export interface ILawTableResponse {
  count: number
  result: ILawTableRecord[]
}
