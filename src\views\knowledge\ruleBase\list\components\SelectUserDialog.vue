<template>
  <el-dialog
    v-model="visible"
    class="dialog-wrap"
    title="可见范围"
    append-to-body
    :close-on-click-modal="false"
    width="600px"
    show-close
    @close="close"
  >
    <div class="box">
      <OrgTree
        v-if="visible"
        ref="refOrgTree"
        :default-selects="selectedUser"
        :is-show-icon="false"
        @select-change="selectChangeHandle"
      />
    </div>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
// 流程页面暂时内嵌，等评审开发完

import { ref } from 'vue'
import OrgTree from '@/components/orgTree/index.vue'

// 定义组件引用类型
interface OrgTreeInstance {
  rightSelectList: any[]
}

let _resolve: ((value: any[]) => void) | null = null
const visible = ref(false)

// const selectedUser = ref(['1904375991401320448', '1904739423883497472']);
const selectedUser = ref<string[]>([])
const refOrgTree = ref<OrgTreeInstance | null>(null)

function getSelectedUser() {
  return refOrgTree.value?.rightSelectList || []
}

function openDialog(selecteds: string[]) {
  selectedUser.value = selecteds
  visible.value = true
  return new Promise<any[]>((resolve) => {
    _resolve = resolve
  })
}

function closeDialog() {
  visible.value = false
}

function close() {
  visible.value = false
}

function selectChangeHandle(val: any) {
  // 处理选择变化
}

function submit() {
  const arr = getSelectedUser()
  _resolve && _resolve(arr)
  close()
}

// 暴露给父组件的方法
defineExpose({
  openDialog,
  closeDialog,
})
</script>
<style lang="scss" scoped>
.box {
  height: 500px;
}
</style>
