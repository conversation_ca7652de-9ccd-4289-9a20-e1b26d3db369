<template>
  <!-- 引用示例：<SvgIcons name="slogan" width="130px" height="38px" /> -->
  <svg aria-hidden="true" :width="width" :height="height" :fill="color">
    <use :href="symbolId" style="fill: currentcolor" />
  </svg>
</template>
<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '16px',
  },
  height: {
    type: String,
    default: '16px',
  },
})
const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>
