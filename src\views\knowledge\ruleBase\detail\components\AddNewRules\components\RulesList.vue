<template>
  <div class="page-wrap-list">
    <!-- 审查清单 -->
    <div ref="refReviewList" v-loading="loading" class="review-list">
      <div class="review-list-title">审查清单</div>
      <div
        v-for="item in reviewList"
        :key="item.id"
        class="review-list-item"
        :class="{ 'item-active': selectedReviewRule === item.id }"
        @click="getReviewRules(item)"
      >
        {{ item.ruleListName }}({{ item.rulePosition }})
      </div>
    </div>
    <!-- 审查规则 - 树形结构 -->
    <div v-loading="loadingRules" class="review-rules">
      <div class="review-rules-title">审查规则</div>
      <el-input v-model="filterText" placeholder="请输入审查点名称"> </el-input>
      <el-tree
        ref="refReviewTree"
        :data="reviewRulesData"
        :props="reviewRulesProps"
        :filter-node-method="filterReviewNode"
        node-key="id"
        default-expand-all
        show-checkbox
        highlight-current
        @check="handleCheck"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'

// 定义 props 接口
interface Props {
  reviewList?: any[]
  loading: boolean
  reviewRules?: any[]
  loadingRules?: boolean
}

// 定义 emits 接口
interface Emits {
  getReviewRuleData: [id: any]
  getRuleDetails: [data: any]
  getSelectedNum: [num: number]
}

// 定义组件实例类型
interface ElTreeInstance {
  setCurrentKey: (key: string | number) => void
  filter: (value: string) => void
  getCheckedNodes: (leafOnly?: boolean, includeHalfChecked?: boolean) => any[]
}

// 定义 props 默认值
const props = withDefaults(defineProps<Props>(), {
  reviewList: () => [],
  reviewRules: () => [],
  loadingRules: false,
})

// 定义 emits
const emit = defineEmits<Emits>()

// 组件引用
const refReviewList = ref<HTMLElement | null>(null)
const refReviewTree = ref<ElTreeInstance | null>(null)

// 响应式数据
const selectedReviewRule = ref<string | number | null>(null) // 当前选中项的ID
const reviewRulesData = ref<any[]>([])
const filterText = ref('')

// 树形结构配置
const reviewRulesProps = {
  label: 'ruleTypeName',
  children: 'child',
}

// 获取审查规则
const getReviewRules = (item: any) => {
  selectedReviewRule.value = item.id
  if (selectedReviewRule.value !== null) {
    emit('getReviewRuleData', selectedReviewRule.value)
  }
  reviewRulesData.value = props.reviewRules
}

// 默认选中第一项
const initData = async () => {
  if (props.reviewList.length > 0) {
    selectedReviewRule.value = props.reviewList[0].id
    if (selectedReviewRule.value !== null) {
      emit('getReviewRuleData', selectedReviewRule.value)
    }
  }
}

// 监听 reviewRules 变化
watch(
  () => props.reviewRules,
  (newVal) => {
    reviewRulesData.value = newVal
  },
  { immediate: true },
)

// 监听 reviewRulesData 默认选中第一条子节点
watch(
  () => reviewRulesData.value,
  (newVal) => {
    if (newVal.length > 0) {
      nextTick(() => {
        const firstNode = newVal[0]
        if (firstNode.child && firstNode.child.length > 0) {
          const firstChildId = firstNode.child[0].id
          refReviewTree.value?.setCurrentKey(firstChildId) // 高亮选中节点
          emit('getRuleDetails', firstNode.child[0]) // 触发点击事件（可选）
        }
      })
    }
  },
  { immediate: true },
)

// 监听搜索文本变化
watch(filterText, (newVal) => {
  refReviewTree.value?.filter(newVal)
})

// 树形节点点击事件
const handleNodeClick = (data: any) => {
  emit('getRuleDetails', data)
}

// 属性节点筛选事件
const filterReviewNode = (value: string, data: any) => {
  if (!value) return true
  return data.ruleTypeName.indexOf(value) !== -1
}

// 以数组的形式获取勾选的属性节点
const getCheckedRulesData = () => {
  // 只获取叶子节点（二级节点）
  const leafNodes = refReviewTree.value?.getCheckedNodes(true, false)
  return leafNodes || []
}

// 树形节点勾选事件
const handleCheck = (_data: any, checked: any) => {
  const num = checked.checkedNodes.filter((node: any) => !node.child || !node.child.length).length
  emit('getSelectedNum', num)
}

// 暴露给父组件的方法
defineExpose({
  initData,
  getCheckedRulesData,
})

// 组件挂载时初始化数据
onMounted(async () => {
  await initData()
})
</script>

<style scoped lang="scss">
.page-wrap-list {
  display: flex;
  flex: 1;
  height: 100%;
  .review-list-title,
  .review-rules-title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
  }
  .review-list {
    flex: 1;
    height: 100%;
    padding: 10px 10px 0 4px;
    overflow-y: auto;
    border-right: 1px solid #e8e8e8;
    .review-list-item {
      padding: 0 5px;
      margin-bottom: 5px;
      font-size: 16px;
      line-height: 28px;
      border-radius: 5px;
      &:hover {
        cursor: pointer;
        background-color: #f2f2f2;
      }
    }
    .item-active {
      background-color: #f4effe;
    }
  }
  .review-rules {
    flex: 1;
    height: 100%;
    padding: 10px;
    overflow: auto;
    border-right: 1px solid #e8e8e8;
  }
}
</style>
