import request from '@/services'
import type { IResponse } from '@/services'

// 标签数据接口
export interface LabelItem {
  id?: string
  labelName?: string
  enableStatus?: boolean
  bizType?: string
  createUser?: string
}

// 业务类型接口
export interface BizTypeItem {
  code: string
  name: string
}

// 分页查询参数接口
export interface LabelPageParams {
  pageNum: number
  pageSize: number
  labelName?: string
  enableStatus?: boolean
}

// 分页查询响应接口
export interface LabelPageResponse {
  records: LabelItem[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 新增标签
 */
export function saveLabel(data: LabelItem): Promise<IResponse<any>> {
  return request({
    url: '/label/save',
    method: 'post',
    data,
  })
}

/**
 * 查看标签详情
 */
export function getLabelDetail(id: string): Promise<IResponse<LabelItem>> {
  return request({
    url: `/label/detail/${id}`,
    method: 'post',
  })
}

/**
 * 更新标签
 */
export function updateLabel(data: LabelItem): Promise<IResponse<any>> {
  return request({
    url: '/label/update',
    method: 'post',
    data,
  })
}

/**
 * 标签列表分页查询
 */
export function getLabelPage(data: LabelPageParams): Promise<IResponse<LabelPageResponse>> {
  return request({
    url: '/label/page',
    method: 'post',
    data,
  })
}

/**
 * 变更标签启用禁用状态
 */
export function changeLabelStatus(data: LabelItem): Promise<IResponse<any>> {
  return request({
    url: '/label/changeEnableStatus',
    method: 'post',
    data,
  })
}

/**
 * 删除标签
 */
export function deleteLabel(id: string): Promise<IResponse<any>> {
  return request({
    url: `/label/delete/${id}`,
    method: 'delete',
  })
}

/**
 * 获取业务类型列表
 */
export function getBizTypes(): Promise<IResponse<BizTypeItem[]>> {
  return request({
    url: '/label/bizTypes',
    method: 'get',
  })
}
