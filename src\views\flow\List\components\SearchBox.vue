<script setup lang="ts">
import type { IWorkflowQueryData } from '../../api/index.ts'

const emits = defineEmits(['search'])
const initData: IWorkflowQueryData = {
  wfName: '',
  wfDescription: '',
}

const searchForm = ref<IWorkflowQueryData>({ ...initData })

const handleReset = () => {
  searchForm.value = { ...initData }
  handleSearch()
}

const handleSearch = () => {
  const params: IWorkflowQueryData = {
    ...searchForm.value,
  }
  emits('search', params)
}
</script>

<template>
  <div class="condition">
    <span class="condition-label">工作流名称</span>
    <el-input v-model="searchForm.wfName" clearable placeholder="请输入搜索内容" />
  </div>
  <div class="condition">
    <span class="condition-label">工作流描述</span>
    <el-input v-model="searchForm.wfDescription" clearable placeholder="请输入搜索内容" />
  </div>
  <div class="btn-wrap">
    <el-button class="btn" type="primary" @click="handleSearch">查 询</el-button>
    <el-button class="btn" @click="handleReset">重 置</el-button>
  </div>
</template>
