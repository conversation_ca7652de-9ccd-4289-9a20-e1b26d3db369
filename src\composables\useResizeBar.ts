// src/composables/useResizeBar.ts
import { ref, onMounted, onBeforeUnmount, type Ref } from 'vue'

interface UseResizeBarOptions {
  initialWidth?: number
  minWidth?: number
  maxWidth?: number | (() => number)
  storageKey?: string
}

interface UseResizeBarReturn {
  width: Ref<number>
  isDragging: Ref<boolean>
  handleMouseDown: (e: MouseEvent) => void
}

export function useResizeBar(options: UseResizeBarOptions = {}): UseResizeBarReturn {
  // 默认配置和用户配置合并
  const {
    initialWidth = 300,
    minWidth = 200,
    maxWidth = () => window.innerWidth - 400,
    storageKey = 'leftPaneWidth',
  } = options

  // 响应式状态
  const width = ref<number>(initialWidth)
  const isDragging = ref<boolean>(false)

  // 鼠标按下事件处理
  const handleMouseDown = (e: MouseEvent): void => {
    isDragging.value = true
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
  }

  // 鼠标移动事件处理
  const handleMouseMove = (e: MouseEvent): void => {
    if (!isDragging.value) return

    // 计算新宽度
    let newWidth = e.clientX

    // 限制最小最大宽度
    newWidth = Math.max(minWidth, newWidth)
    newWidth = Math.min(typeof maxWidth === 'function' ? maxWidth() : maxWidth, newWidth)

    width.value = newWidth
  }

  // 鼠标松开事件处理
  const handleMouseUp = (): void => {
    if (isDragging.value) {
      // 保存宽度到本地存储
      localStorage.setItem(storageKey, width.value.toString())
    }
    isDragging.value = false
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  // 组件挂载时
  onMounted(() => {
    // 从本地存储恢复宽度
    const savedWidth = localStorage.getItem(storageKey)
    if (savedWidth) {
      width.value = parseInt(savedWidth)
    }

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  })

  // 组件卸载时
  onBeforeUnmount(() => {
    // 移除全局事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  })

  // 返回需要的状态和方法
  return {
    width,
    isDragging,
    handleMouseDown,
  }
}
