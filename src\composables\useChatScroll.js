import { nextTick } from 'vue'
import { throttle } from 'knox-tool'

export default function useChatScroll(scrollWrap) {
  let chatScrollFlag = true

  function pauseChatScroll(e) {
    if (e.wheelDeltaY > 8) {
      chatScrollFlag = false
    } else {
      let top = scrollWrap.value.scrollTop
      let sh = scrollWrap.value.scrollHeight
      let h = scrollWrap.value.clientHeight
      if (sh - h - top < 16) {
        chatScrollFlag = true
      }
    }
  }

  function scrollToBottom() {
    scrollThrottle()
  }
  const scrollThrottle = throttle(scrollTo, 500)
  async function scrollTo() {
    await nextTick()
    // 获取 simplebar 的滚动容器
    if (!scrollWrap.value || !chatScrollFlag) return

    const start = scrollWrap.value.scrollTop
    const end = scrollWrap.value.scrollHeight
    const duration = 500 // 动画持续时间，单位：毫秒
    const startTime = performance.now()

    const animateScroll = (currentTime) => {
      const elapsed = currentTime - startTime
      if (elapsed < duration) {
        // 使用 ease-in-out 缓动函数
        const progress = elapsed / duration
        const ease = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress
        scrollWrap.value.scrollTop = start + (end - start) * ease
        requestAnimationFrame(animateScroll)
      } else {
        scrollWrap.value.scrollTop = end
      }
    }

    requestAnimationFrame(animateScroll)
  }

  return {
    scrollToBottom,
    pauseChatScroll,
  }
}
