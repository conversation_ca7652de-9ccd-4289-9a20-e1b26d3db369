import axios from 'axios'
import { BASE_API } from '@/utils/config'
import { useAppStore } from '@/stores/app'

export type IResponse<T> = {
  code: string
  data: T
  message: string
  success: boolean
}

// create an axios instance
const service = axios.create({
  baseURL: BASE_API,
  timeout: 30000000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (config.method === 'get') {
      if (!config.params) {
        config.params = { _t: +new Date() }
      } else {
        config.params._t = +new Date()
      }
    }
    const appStore = useAppStore()
    if (appStore.token) {
      config.headers['csrf_token'] = appStore.token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    if (response.data instanceof Blob) return response
    const res = response.data

    if (res.code == 200) return res
    if (res.code == '000000') return res //////// 临时处理，后端请求成功返回code为000000
    if (res.code.toString().length === 5) {
      return res
    }
    errorHandle(+res.code, res.msg || res.message)

    if ([401, 403].includes(+res.code)) {
      return logout()
    }
    return Promise.reject(res.msg || res.message || 'Error')
  },
  (error) => {
    console.log(error)
    if (error.message && error.message === '路由跳转取消请求')
      return new Promise(() => {
        console.log('中断')
      })
    if (error.response && error.response.data) {
      error.message = error.response.data.msg || error.response.data.error || '服务器内部错误'
    } else if (error.message && error.message === 'Request failed with status code 405') error.message = '无权限访问'
    if ([401, 403].includes(+error.response.status)) {
      return logout()
    }
    if (error.response.data instanceof Blob) {
      const reader = new FileReader()
      reader.onload = function (event) {
        const result = event.target?.result
        let obj = { msg: '服务器内部错误' }
        try {
          obj = JSON.parse(result as string)
        } catch (error) {
        } finally {
          ElMessage({
            message: obj.msg,
            type: 'error',
            duration: 5 * 1000,
          })
          return Promise.reject(obj.msg)
        }
      }
      reader.readAsText(error.response.data)
    } else {
      ElMessage({
        message: error.message,
        type: 'error',
        duration: 5 * 1000,
      })
      return Promise.reject(error.message || 'Error')
    }
  },
)

function logout() {
  ElMessage({
    message: '您的会话已过期，请重新登录',
    type: 'error',
    duration: 1000,
  })
  const appstore = useAppStore()
  appstore.logout()
}

/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败的状态码
 */
const errorHandle = (status: number, other?: string) => {
  let errorInfo = ''
  // 状态码判断
  switch (status) {
    case 401:
      errorInfo = '您的会话已过期，请重新登录'
      break
    case 403:
      errorInfo = '您的权限受到限制，请咨询管理员获取权限'
      break
    case 404:
      errorInfo = '错误的参数或请求地址，请检查'
      break
    case 411:
      errorInfo = '您的会话已过期，请重新登录'
      break
    case 500:
      errorInfo = other || '服务器内部错误'
      break
    //////// 暂定，后端统一状态码后需要重新修改
    case 1230114:
      errorInfo = '用户密码错误'
      break
    default:
      errorInfo = other || '网络异常，请稍后再试'
  }
  ElMessage.closeAll()
  ElMessage({
    message: errorInfo,
    type: 'error',
    duration: 5 * 1000,
  })
}

export default service
