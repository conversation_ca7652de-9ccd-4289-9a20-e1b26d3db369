<template>
  <div class="box-wrap">
    <div class="condition">
      <span class="condition-label">清单名称</span>
      <el-input
        v-model="searchForm.name"
        clearable
        placeholder="请输入清单名称"
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      />
    </div>
    <div class="condition">
      <span class="condition-label">合同类型</span>
      <el-select
        v-model="searchForm.contractTypes"
        filterable
        placeholder="全部"
        :loading="contractTypeLoading"
        clearable
        multiple
        collapse-tags
        collapse-tags-tooltip
        @focus="handleContractTypeFocus"
        @change="handleSearch"
      >
        <el-option v-for="item in contractTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">处理状态</span>
      <el-select v-model="searchForm.analysisProgress" placeholder="请选择" clearable @change="handleSearch">
        <el-option
          v-for="item in props.statusOption"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">可见范围</span>
      <el-select v-model="searchForm.visibilityScope" placeholder="请选择" clearable @change="handleSearch">
        <el-option
          v-for="item in visibilityScopeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">数据来源</span>
      <el-select v-model="searchForm.dataSource" placeholder="请选择" clearable @change="handleSearch">
        <el-option
          v-for="item in dataSourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">创建人</span>
      <last-update-by v-model:last-updated-by="searchForm.createBy" :placeholder="'创建人'"></last-update-by>
    </div>

    <div class="btn-wrap">
      <el-button class="btn" type="primary" @click.stop="handleSearch">查 询</el-button>
      <el-button class="btn" @click="handleReset">重 置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { queryStandpoint, queryContractTypes } from '@/services/ruleBaseApi'
import LastUpdateBy from '@/components/LastUpdateBy.vue'

// 定义类型接口
interface SearchForm {
  name: string
  standpoint: string
  createBy: string
  analysisProgress: string
  contractTypes: string[]
  visibilityScope: string
  dataSource: string
}

interface StatusOption {
  value: string
  label: string
  color?: string
}

interface SearchParams {
  ruleListName?: string
  rulePosition?: string
  createBy?: string
  analysisProgress?: string
  contractTypes?: string[]
  visibilityScope?: string
  dataSource?: string
}

// 定义 props
interface Props {
  statusOption?: StatusOption[]
}

const props = withDefaults(defineProps<Props>(), {
  statusOption: () => [],
})

// 定义 emits
const emit = defineEmits<{
  search: [params: SearchParams]
}>()

// 响应式数据
// const standpointOptions = ref<{ label: string; value: string }[]>([])
// const standpointLoading = ref(false)
const contractTypeOptions = ref<{ label: string; value: string }[]>([])
const contractTypeLoading = ref(false)

// 可见范围选项
const visibilityScopeOptions = ref([
  { label: '公开可见', value: 'public' },
  { label: '后台可见', value: 'private' },
])

// 数据来源选项
const dataSourceOptions = ref([
  { label: '手动导入', value: 'manual' },
  { label: 'iTerms-SaaS', value: 'iterms-saas' },
])

const initData: SearchForm = {
  name: '',
  standpoint: '',
  createBy: '',
  analysisProgress: '',
  contractTypes: [],
  visibilityScope: '',
  dataSource: '',
}

const searchForm = ref<SearchForm>(JSON.parse(JSON.stringify(initData)))

// 方法
function handleReset() {
  searchForm.value = JSON.parse(JSON.stringify(initData))
  handleSearch()
}

async function handleSearch() {
  console.log('SearchBox handleSearch 被调用')

  // 使用 nextTick 确保 v-model 绑定的数据已经更新
  await nextTick()

  console.log('当前表单数据:', searchForm.value)

  const searchParams: SearchParams = {
    ruleListName: searchForm.value.name || undefined,
    rulePosition: searchForm.value.standpoint || undefined,
    createBy: searchForm.value.createBy || undefined,
    analysisProgress: searchForm.value.analysisProgress || undefined,
    contractTypes: searchForm.value.contractTypes.length > 0 ? searchForm.value.contractTypes : undefined,
    visibilityScope: searchForm.value.visibilityScope || undefined,
    dataSource: searchForm.value.dataSource || undefined,
  }

  console.log('构建的搜索参数:', searchParams)
  emit('search', searchParams)
}

// async function handleFocus() {
//   standpointLoading.value = true
//   try {
//     const { data } = await queryStandpoint({})
//     standpointOptions.value = data.map((item: string) => ({ label: item, value: item }))
//   } finally {
//     standpointLoading.value = false
//   }
// }

async function handleContractTypeFocus() {
  contractTypeLoading.value = true
  try {
    return
    const { data } = await queryContractTypes({})
    contractTypeOptions.value = data.map((item: any) => ({
      label: item.name || item.typeName || item.label,
      value: item.id || item.typeId || item.value,
    }))
  } finally {
    contractTypeLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.box-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px;
  padding: 16px 16px 16px 0;
  background-color: #fff;
  .btn {
    font-size: 14px;
  }
  .condition {
    display: flex;
    align-items: center;
    width: 25%;
    &-label {
      width: 100px;
      padding-right: 12px;
      color: #606266;
      text-align: right;
    }
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100% !important;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .btn-wrap {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    padding-left: 25px;
  }
}
</style>
