<template>
  <div class="left-tree">
    <el-input
      v-model="filterText"
      placeholder="请输入审查项"
      class="left-input"
      :prefix-icon="Search"
      clearable
    ></el-input>
    <div class="left-top">
      <span class="left-top-text">审查类型</span>
      <el-icon class="btn-icon" @click="handleCreate(dataTree)"><Plus /></el-icon>
    </div>
    <div v-loading="loading" class="tree-wrap">
      <draggable v-model="dataTree" group="items" @end="endDrag" item-key="id">
        <template #item="{ element: item, index: idx }">
          <div v-show="!item.isHide" class="tree-node">
            <div class="tree-node-top" :class="{ 'is-active': item.isActive }">
              <el-icon class="el-icon-menu btn-icon option-top"><Menu /></el-icon>
              <div v-if="!item.isEdit" class="tree-node-label level1" @click="handleUnfold(item)">
                <TextOverTip style="width: 100%" :content="item.label" />
                <el-icon class="btn-icon" v-if="!item.unfold"><ArrowDown /></el-icon>
                <el-icon class="btn-icon" v-else><ArrowUp /></el-icon>
              </div>
              <el-input
                v-else
                id="itemInput"
                v-model="item.label"
                class="tree-node-input"
                @blur="handleEditFinish(item)"
                @focus="handleFocus(item)"
                @keyup.enter="handleEditFinish(item)"
              ></el-input>

              <el-dropdown @command="handleCommand" trigger="click">
                <el-icon class="btn-icon"><MoreFilled /></el-icon>
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="beforeHandleCommand(item, 'create')"> 新增 </el-dropdown-item>
                    <el-dropdown-item :command="beforeHandleCommand(item, 'edit')">重命名</el-dropdown-item>
                    <el-dropdown-item :command="beforeHandleCommand(item, 'del', dataTree)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div class="tree-node-body">
              <draggable
                v-if="item.children && item.children.length"
                :list="item.children"
                group="nodes"
                @start="dragStart"
                @end="dragEnd"
                @change="(event: any) => changeDrag(event, idx)"
                item-key="id"
              >
                <template #item="{ element: node }">
                  <div v-if="item.unfold" v-show="!node.isHide" class="tree-node">
                    <div
                      class="tree-node-top cursor"
                      :class="{ 'is-active': node.isActive }"
                      @click="(e) => handleChangeRule(e, node)"
                    >
                      <div class="tree-node-label">
                        <i class="node-dot" :class="{ 'dot-active': node.isSaved }"></i>
                        <TextOverTip v-if="!node.isEdit" style="width: 100%" :content="node.label" placement="right" />
                        <el-input
                          v-else
                          id="itemInput"
                          v-model="node.label"
                          class="tree-node-input"
                          @blur="handleEditFinish(node)"
                          @focus="handleFocus(node)"
                          @keyup.enter="handleEditFinish(node)"
                        ></el-input>
                      </div>
                      <el-dropdown @command="handleCommand" trigger="click">
                        <el-icon class="btn-icon"><MoreFilled /></el-icon>
                        <template v-slot:dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item :command="beforeHandleCommand(node, 'edit')">重命名</el-dropdown-item>
                            <el-dropdown-item :command="beforeHandleCommand(node, 'del', item.children)"
                              >删除</el-dropdown-item
                            >
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </template>
                <template #footer>
                  <div v-if="!item.unfold" :class="{ 'node-dragging': isDragging }"></div>
                </template>
              </draggable>
            </div>
          </div>
        </template>
      </draggable>
    </div>
    <CreateDialog ref="refCreate" @handleReviewItem="handleChangeRule" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, inject } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { queryDictionary, createRuleList, delRuleId, uploadSortList } from '@/services/ruleBaseApi'
import TextOverTip from '@/components/TextOverTip.vue'
import CreateDialog from './components/createDialog.vue'
import draggable from 'vuedraggable'

// 获取路由和注入的数据
const route = useRoute()
const refContent = inject('refContent') as any
const ruleListName = inject('ruleListName') as any
const filterText = ref('')
const loading = ref(false)
const ruleListId = route.query.id
let dataSource = ''
// 定义数据类型
interface TreeNode {
  id: string
  parentId?: string
  label: string
  isEdit: boolean
  isActive: boolean
  isHide: boolean
  unfold?: boolean
  isSaved?: boolean
  children?: TreeNode[]
}

const dataTree = ref<TreeNode[]>([])

// 监听搜索文本变化
watch(filterText, (newValue) => {
  if (newValue) {
    dataTree.value.forEach((item) => {
      item.children?.forEach((node) => {
        if (!node.label.includes(newValue)) node.isHide = true
        else node.isHide = false
      })

      if (item.children?.find((node) => !node.isHide)) {
        item.isHide = false
      } else {
        item.isHide = true
      }
    })
  } else {
    dataTree.value.forEach((item) => {
      item.isHide = false
      item.children?.forEach((node) => {
        node.isHide = false
      })
    })
  }
})
// 拖拽结束处理
async function endDrag() {
  const params = dataTree.value.map((item) => {
    return item.id
  })
  await uploadSortList(params)
  console.log('拖拽排序:', params)
}

// 拖拽变化处理
async function changeDrag(e: any, index: number) {
  if (e.added || e.moved) {
    const params = dataTree.value[index].children?.map((item) => {
      return item.id
    })
    try {
      await uploadSortList(params)
      console.log('子项排序:', params)
    } catch (error) {
      dataTree.value = copyData
    }
  }
}
// 获取树形数据
async function getTreeData() {
  try {
    loading.value = true
    const { data } = await queryDictionary({ ruleListId })
    ruleListName.value = data.ruleListName
    const arr: TreeNode[] = []
    data.ruleTypeList.forEach((item: any, idx: number) => {
      const obj: TreeNode = {
        id: item.id,
        parentId: item.parentId,
        label: item.ruleTypeName,
        isEdit: false,
        isActive: false,
        isHide: false,
        unfold: false,
        children: [],
      }
      arr.push(obj)
      if (item.child?.length) {
        item.child.forEach((node: any) => {
          const objNode: TreeNode = {
            id: node.id,
            parentId: node.parentId,
            label: node.ruleTypeName,
            isEdit: false,
            isHide: false,
            isActive: false,
            isSaved: !!node.ruleItem,
          }
          arr[idx].children!.push(objNode)
        })
      }
    })
    dataTree.value = arr
    if (dataTree.value.length && dataTree.value[0]?.children?.length) {
      handleChangeRule(null, dataTree.value[0].children[0])
    }
  } finally {
    loading.value = false
  }
}
// 展开/折叠处理
function handleUnfold(item: TreeNode) {
  item.unfold = !item.unfold
}

// 命令处理前置函数
function beforeHandleCommand(val: TreeNode, command: string, parent?: TreeNode[]) {
  return {
    val,
    command,
    parent,
  }
}

// 命令处理
function handleCommand(command: any) {
  switch (command.command) {
    case 'create':
      handleCreate(command.val)
      break
    case 'edit':
      handleEdit(command.val)
      break
    case 'del':
      handleRemove(command.val, command.parent)
      break
  }
}

// 创建对话框引用和处理
const refCreate = ref()
function handleCreate(val: TreeNode | TreeNode[]) {
  refCreate.value?.open(val)
}

// 编辑处理
function handleEdit(node: TreeNode) {
  node.isEdit = true
  nextTick(() => {
    const input = document.querySelector('#itemInput') as HTMLInputElement
    input?.focus()
  })
}

// 编辑完成处理
async function handleEditFinish(node: TreeNode) {
  if (!node.isEdit) return
  if (node.label) {
    try {
      const params = {
        ruleListId,
        id: node.id,
        parentId: node.parentId,
        ruleTypeName: node.label,
      }
      await createRuleList(params)
      ElMessage.success('更新成功')
      console.log('更新参数:', params)
    } catch (error) {
      ElMessage.error('更新失败')
    }
  } else {
    node.label = dataSource
  }
  node.isEdit = false
}

// 聚焦处理
function handleFocus(node: TreeNode) {
  dataSource = node.label
}

// 删除处理
async function handleRemove(val: TreeNode, parent: TreeNode[]) {
  try {
    await ElMessageBox.confirm('删除当前分类，该分类下的所有数据也会删除，确定删除吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 请求接口
    try {
      const params = {
        ruleTypeId: val.id,
      }
      await delRuleId(params)
      ElMessage.success('删除成功')
      console.log('删除参数:', params)
    } catch (error) {
      ElMessage.error('删除失败')
    }

    const index = parent.findIndex((item) => item.id === val.id)
    parent.splice(index, 1)
    if (val.isActive && dataTree.value.length) {
      handleChangeRule(null, dataTree.value[0]?.children?.[0])
    }
  } catch {
    // 用户取消删除
  }
}

// 规则变更处理
function handleChangeRule(e: any, node?: TreeNode) {
  if (!node) {
    refContent.value?.getRuleData(node)
    return
  }
  if (node.isActive) {
    refContent.value?.getRuleData(node)
    return
  }
  if (e?.target.tagName === 'INPUT') return

  dataTree.value.forEach((item) => {
    item.isActive = false
    item.children?.forEach((node) => {
      node.isActive = false
    })
  })

  node.isActive = true
  dataTree.value.forEach((item) => {
    const flag = item.children?.some((node) => node.isActive)
    if (flag) {
      item.isActive = true
    }
  })

  refContent.value?.getRuleData(node)
}

// 拖拽相关
const isDragging = ref(false)
let copyData: TreeNode[] = []

function dragStart() {
  copyData = JSON.parse(JSON.stringify(dataTree.value))
  isDragging.value = true
}

function dragEnd() {
  isDragging.value = false
}

// 暴露给模板的方法
defineExpose({
  getTreeData,
})
</script>

<style lang="scss" scoped>
.left-tree {
  display: flex;
  flex-direction: column;
  width: 272px;
  border-right: 1px solid #e9e9e9;
  .left-input {
    width: calc(100% - 25px) !important;
  }
  .left-top {
    display: flex;
    justify-content: space-between;
    height: 40px;
    padding: 12px 6px 12px 16px;
    &-text {
      font-size: 16px;
      font-weight: bold;
    }
  }
  :deep(.el-input) {
    width: 248px;
    margin: 12px 0 12px 12px;
  }
  .tree-wrap {
    flex: 1;
    height: 0;
    overflow-y: auto;
    .tree-node-top {
      justify-content: flex-start;
      width: 100%;
      text-align: left;
    }
  }
}
.btn-icon {
  margin-right: 5px;
  font-size: 16px;
  color: #bdbdbd;
  cursor: pointer;
}
.cursor {
  cursor: pointer;
}
.tree-node {
  position: relative;
  flex: 1;
  width: 100%;
  &-top {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding: 12px 25px 12px 16px;
    &:hover {
      background-color: #f1f5ff;
      .option-top {
        opacity: 1;
      }
    }
    :deep(.el-dropdown) {
      position: absolute;
      right: 4px;
      .el-icon {
        outline: none !important;
        background: none !important;
        border: none !important;
        box-shadow: none !important;
        &:hover {
          background: rgb(0 0 0 / 10%) !important;
          border-radius: 4px;
        }
        &:focus {
          outline: none !important;
          background: none !important;
        }
      }
    }
    .option-top {
      margin-top: 2px;
      margin-right: 6px;
      font-size: 12px;
      opacity: 0;
    }
    .tree-node-label {
      display: flex;
      flex: 1;
      align-items: center;
      height: 100%;
      padding-right: 8px;
      text-overflow: ellipsis;
      white-space: nowrap;
      .tree-node-input {
        flex: 1;
        width: 100%;
        margin-left: 0;
        :deep(.el-input__inner) {
          width: 100%;
          padding-left: 8px;
          text-align: left;
        }
      }
      .node-dot {
        display: inline-block;
        flex-shrink: 0;
        width: 6px;
        height: 6px;
        margin: 0 8px;
        background-color: #bdbdbd;
        border-radius: 50%;
      }
      .dot-active {
        background-color: #1fac78;
      }
    }
    .level1 {
      font-weight: bold;
    }
  }
  .is-active {
    background-color: #f1f5ff;
  }
}
.dropdown-node {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.node-dragging {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
