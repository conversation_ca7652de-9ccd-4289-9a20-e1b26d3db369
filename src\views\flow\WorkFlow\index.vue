<script setup lang="ts">
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import { Tinyflow } from '@iterms/tinyflow-ai-vue'
import '@iterms/tinyflow-ai-vue/dist/index.css'
import ResultBar from './components/ResultBar.vue'
import type { IQueryWorkflowData, IQueryModelList, ISaveWorkflowGraphParams } from '../api/index.ts'
import { queryWorkflowData, queryModelList, saveWorkflowGraph, updateWorkflowBaseInfo } from '../api/index.ts'
import { RESPONSE_CODE_SUCCESS } from '@/constants/index.ts'

const route = useRoute()
const flowID = route.query.id || ''
const agentName = ref('')
const description = ref('')
const tinyflowRef = ref<InstanceType<typeof Tinyflow>>()
const isDone = ref(false)
const resultBarRef = ref()
const initialData = ref({
  nodes: [],
  edges: [],
  viewport: { x: 250, y: 100, zoom: 1 },
})

const workflowName = ref('')
const workflowDescription = ref('')

// 定义转换后的列表项类型
interface ModelOption {
  label: string
  value: string
}
const modelList = ref<ModelOption[]>([])
const getModelList = async () => {
  try {
    const { data } = await queryModelList<IQueryModelList[]>()
    modelList.value = data.map((item) => ({
      label: item.modelName,
      value: item.modelName,
    }))
    return modelList.value
  } catch (err) {
    console.log('获取模型列表失败', err)
    return []
  }
}

const initProvider = {
  // 模型
  llm: getModelList,
  // 知识库
  knowledge: () => [],
  // 搜索引擎
  searchEngine: () => [],
}

const customNodes = ref({
  fileNode: {
    title: '文件解析节点',
    description: '上传文件解析',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 17V19C17 19.5523 17.4477 20 18 20C18.5523 20 19 19.5523 19 19V4H5V15H3V3C3 2.44772 3.44772 2 4 2H20C20.5523 2 21 2.44772 21 3V19C21 20.6569 19.6569 22 18 22H4C2.34315 22 1 20.6569 1 19V17H17Z"></path></svg>',
    group: 'base' as const,
    sortNo: 100,
    forms: [
      {
        type: 'input' as const,
        name: 'fileParsing',
        label: '文件名',
        placeholder: '请上传要解析的文件',
      },
    ],
  },
})

const workflowInfo = ref<IQueryWorkflowData>()

const editVisible = ref(false)

const getInfoById = async () => {
  if (!flowID) {
    isDone.value = false
    return
  }
  try {
    const { data } = await queryWorkflowData<IQueryWorkflowData>(flowID as string)
    console.log('获取工作流详情', data)
    workflowInfo.value = data
    agentName.value = data.wfName
    description.value = data.wfDescription
    if (data.wfGraph) {
      initialData.value = JSON.parse(data.wfGraph)
    }
    isDone.value = true
  } catch (err) {
    console.error('获取工作流详情失败', err)
  }
}

const getData = () => {
  return tinyflowRef.value?.getData()
}

const init = async () => {
  await getInfoById()
  // await getModelList()
}

// 保存
const save = async () => {
  const data = await getData()
  const graph = JSON.stringify(data)
  if (flowID) {
    const params = { id: flowID as string, wfGraph: graph }
    const res = await saveWorkflowGraph<ISaveWorkflowGraphParams>(params)
    if (res.code == '000000') {
      ElMessage.success('保存成功')
    }
    return params
  }
}

// 试运行
const testRun = () => {
  resultBarRef.value?.testRun()
}

const openEditDialog = () => {
  workflowName.value = agentName.value
  workflowDescription.value = description.value
  editVisible.value = true
}

const confirmEditChat = async () => {
  if (!workflowName.value.trim()) {
    ElMessage.error('工作流名称不能为空')
    return
  }
  if (!workflowDescription.value.trim()) {
    ElMessage.error('工作流描述不能为空')
    return
  }
  try {
    const res = await updateWorkflowBaseInfo(flowID as string, workflowName.value, workflowDescription.value)
    if (res.code === RESPONSE_CODE_SUCCESS) {
      ElMessage.success('工作流信息更新成功')
      agentName.value = workflowName.value
      description.value = workflowDescription.value
    } else {
      ElMessage.error('工作流信息更新失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '工作流信息更新失败')
  } finally {
    editVisible.value = false
  }
}

onMounted(async () => {
  await init()
})
</script>

<template>
  <div class="flow-wrapper">
    <HeaderBox>
      <template #header-title>
        <div>{{ agentName }}</div>
      </template>
      <template #head-icon>
        <img class="icon-edit" src="/src/assets/icons/svg/edit.svg" @click="openEditDialog" />
      </template>
      <template #header-desc>
        <div>{{ description }}</div>
      </template>

      <template #option>
        <span>
          <el-button type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="testRun">试运行</el-button>
        </span>
      </template>
    </HeaderBox>
    <Tinyflow
      v-if="isDone"
      ref="tinyflowRef"
      class="h-full"
      :provider="initProvider"
      :data="initialData"
      :custom-nodes="customNodes"
    />
    <ResultBar ref="resultBarRef" :id="flowID as string" :get-data="getData" />
    <el-dialog v-model="editVisible" @close="editVisible = false" width="400px">
      <template #header>
        <div>编辑工作流名称和描述</div>
      </template>
      <div class="input-wrapper">
        <el-input v-model="workflowName" class="" :clearable="false" />
        <el-input v-model="workflowDescription" class="desc-input"></el-input>
      </div>
      <template #footer>
        <div style="text-align: right">
          <el-button size="small" @click="editVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="confirmEditChat">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.flow-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
}
.icon-edit {
  width: 1rem;
  height: 1rem;
  margin-left: 0.25rem;
  cursor: pointer;
}
.desc-input {
  margin-top: 0.5rem;
}
.h-full {
  height: 100% !important;
}
.p16 {
  padding: 16px;
}
</style>
