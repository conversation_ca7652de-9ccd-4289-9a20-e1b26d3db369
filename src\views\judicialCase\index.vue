<script setup lang="ts">
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'

import type { ILawCaseSearchArgs } from './types'

const tableBox = ref()
function search(arg: ILawCaseSearchArgs) {
  tableBox.value?.query(arg)
}
</script>

<template>
  <div class="diff-wrap">
    <SearchBox @search="search"></SearchBox>
    <TableBox ref="tableBox"></TableBox>
  </div>
</template>

<style lang="scss" scoped>
.diff-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 1rem;
}
</style>
