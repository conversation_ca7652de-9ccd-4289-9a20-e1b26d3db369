<script setup lang="ts">
import { type FormInstance } from 'element-plus'
import { saveModelInfo, queryModelDetail, editModelInfo } from '../api/index.ts'
import type { IModelType, ISaveModelInfoParams, IModel, IEditModelInfoParams } from '../api/index.ts'

defineProps<{
  modelTypeList: IModelType[]
}>()

const emits = defineEmits(['updateList'])

const modelID = ref('')
const title = computed(() => {
  return modelID.value ? '编辑模型' : '新增模型'
})

const visible = ref(false)
const ruleFormRef = ref<FormInstance>()

const formData = ref<ISaveModelInfoParams>({
  modelName: '',
  modelType: 0,
  baseUrl: '',
  apiKey: '',
  remark: '',
})

const rules = {
  modelName: [
    {
      required: true,
      message: '请输入模型名称',
      trigger: 'blur',
    },
  ],
  modelType: [
    {
      required: true,
      message: '选择模型',
      trigger: 'blur',
    },
  ],
  apiKey: [
    {
      required: true,
      message: '请输入大模型API-KEY',
      trigger: 'blur',
    },
  ],
}

const openDialog = async (id?: string) => {
  if (id) {
    modelID.value = id
    await getModelDetail(id)
  }
  visible.value = true
}

// 获取模型详情
const getModelDetail = async (id: string) => {
  try {
    const res = await queryModelDetail<IModel>(id)
    const {
      apiKeyValue: apiKey, // 将 apiKeyValue 重命名为 apiKey
      ...rest
    } = res.data
    formData.value = {
      ...rest,
      apiKey, // 使用重命名后的字段
    }
  } catch (err) {
    console.log('获取模型详情失败', err)
  }
}

const closeDialog = () => {
  visible.value = false
  ruleFormRef.value?.resetFields()
}

// 新增 / 编辑
const saveModelData = async () => {
  await ruleFormRef.value?.validate()
  let params = {}
  if (modelID.value) {
    params = {
      id: modelID.value,
      ...formData.value,
    }
    try {
      const res = await editModelInfo(params as IEditModelInfoParams)
      if (res.code == '000000') {
        emits('updateList')
        ElMessage.success('编辑模型成功')
        closeDialog()
      }
    } catch (error) {
      ElMessage.error('编辑模型失败')
      console.log('验证失败', error)
    }
  } else {
    params = formData.value
    try {
      const res = await saveModelInfo(params as ISaveModelInfoParams)
      if (res.code == '000000') {
        emits('updateList')
        ElMessage.success('新增模型成功')
        closeDialog()
      }
    } catch (error) {
      ElMessage.error('新增模型失败')
      console.log('验证失败', error)
    }
  }
}

defineExpose({
  openDialog,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" width="600px" @close="closeDialog">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" class="right-form" label-position="top">
      <el-form-item label="模型名称" prop="modelName">
        <el-input v-model="formData.modelName" placeholder="请输入模型名称" />
      </el-form-item>
      <el-form-item label="模型类型" prop="modelType">
        <el-select v-model="formData.modelType" placeholder="请选择模型类型">
          <el-option v-for="item in modelTypeList" :key="item.value" :label="item.modelTag" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="请求API地址" prop="baseUrl">
        <el-input v-model="formData.baseUrl" placeholder="请输入请求API地址" />
      </el-form-item>
      <el-form-item label="大模型API-KEY" prop="apiKey">
        <el-input v-model="formData.apiKey" placeholder="请输入大模型API-KEY" />
      </el-form-item>
      <el-form-item label="模型备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入模型备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="saveModelData" :disabled="!formData.modelName">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
