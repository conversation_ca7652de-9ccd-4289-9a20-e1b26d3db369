<script setup lang="ts">
import { type FormInstance } from 'element-plus'
import showPassword from './hooks/showPassword'
import initLogin from './hooks/initLogin'

const loginForm = ref<FormInstance>()

const loginRules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    {
      min: 2,
      max: 60,
      message: '请输入正确的用户名信息',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      min: 6,
      max: 50,
      message: '请输入6-50个字符',
      trigger: 'blur',
    },
  ],
}

const { passwd, passIcon, showPass } = showPassword()
const { loginData, loginLoading, submitForm } = initLogin()
</script>

<template>
  <div class="login-wrap">
    <div class="login-inner">
      <h2 class="login-title">iTerms 管理后台</h2>
      <el-form
        ref="loginForm"
        class="form-login"
        label-position="top"
        status-icon
        :rules="loginRules"
        :model="loginData"
        @keyup.enter="submitForm"
      >
        <el-form-item prop="userName" class="form-input" label="账号">
          <el-input v-model="loginData.userName" maxlength="60" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item prop="password" class="form-input" label="密码">
          <el-input
            v-model="loginData.password"
            :type="passwd"
            autocomplete="off"
            placeholder="请输入密码"
            maxlength="50"
          >
            <template v-slot:suffix>
              <i class="icon-passwd" :class="passIcon" @click="showPass"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="form-btn">
          <el-button class="submit-btn" :loading="loginLoading" type="primary" @click="submitForm">
            {{ loginLoading ? '登 陆 中...' : '登 陆' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.login-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: var(--minor-bg);
  .login-inner {
    width: 25%;
    min-width: 400px;
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    .login-title {
      margin-bottom: 16px;
      color: var(--minor-font);
    }
    .form-login {
      :deep(.el-form-item.form-input) {
        margin-bottom: 24px;
      }
      :deep(.el-form-item.form-btn) {
        margin-bottom: 0;
      }
      :deep(.el-form-item__label) {
        font-size: 16px;
        font-weight: 500;
      }
      :deep(.el-input__inner) {
        height: 48px;
        font-size: 16px !important;
        color: inherit !important;
        background-color: transparent !important;
        transition: background-color 5000s ease-in-out 0s;
        -webkit-text-fill-color: inherit !important;
      }
      :deep(.el-input__validateIcon) {
        display: none;
      }
      .icon-passwd {
        font-size: 20px;
        line-height: 48px;
        cursor: pointer;
      }
      .submit-btn {
        width: 100%;
        height: 48px;
        margin-bottom: 5px;
        font-size: 18px !important;
        letter-spacing: 5px;

        // background-color: var(--main-bg);
        border: 0;
      }

      // .submit-btn:hover {
      //   background-color: #8c61ef;
      // }
    }
  }
}
</style>
