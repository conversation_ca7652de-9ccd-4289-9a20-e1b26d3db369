<script setup lang="ts">
import { requesetLogout } from '@/services/login'
import { ElMessageBox } from 'element-plus'
import { useAppStore } from '@/stores/app'
import EditPassword from './EditPassword.vue'

const appStore = useAppStore()
const editPasswordRef = ref<InstanceType<typeof EditPassword>>()

interface MenuItem {
  label: string
  icon?: string
  iconActive?: string
  path: string
  children?: MenuItem[]
}

// 对应图标待补充 ////////
const menus = ref<MenuItem[]>([
  {
    label: '法律知识库',
    icon: 'icon-nexus-zhishi',
    iconActive: 'icon-nexus-zhishi2',
    path: '/database',
    children: [
      {
        label: '审查清单',
        icon: 'icon-nexus-shencha',
        iconActive: 'icon-nexus-shencha2',
        path: '/knowledge/checklist',
        children: [],
      },
      {
        label: '法律法规',
        icon: 'icon-nexus-shencha',
        iconActive: 'icon-nexus-shencha',
        path: '/regulations',
        children: [],
      },
      {
        label: '司法案例',
        icon: 'icon-nexus-shencha',
        iconActive: 'icon-nexus-shencha',
        path: '/judicialCase',
        children: [],
      },
      { label: '合同范本', icon: '', iconActive: '', path: '/contract-template', children: [] },
      { label: '合同类型管理', icon: '', iconActive: '', path: '/contractType' },
      { label: '标签管理', icon: '', iconActive: '', path: '/tags' },
    ],
  },
  {
    label: '用户组织管理',
    icon: 'icon-nexus-zuzhi',
    iconActive: 'icon-nexus-role',
    path: '/manage',
    children: [
      { label: '用户管理', icon: 'icon-nexus-zuzhi', iconActive: 'icon-nexus-role', path: '/user', children: [] },
    ],
  },
  {
    label: '智能体平台',
    icon: 'icon-nexus-a-3',
    iconActive: 'icon-nexus-a-4',
    path: '/agent',
    children: [
      { label: '工作流', icon: 'icon-nexus-a-3', iconActive: 'icon-nexus-a-4', path: '/flow', children: [] },
      { label: '模型', icon: 'icon-nexus-icon', iconActive: 'icon-nexus-a-1', path: '/model', children: [] },
    ],
  },
])

const route = useRoute()
const defaultActive = ref(route.path)
const currentMenu = computed(() => route.path)

const handleConfig = (command: string) => {
  if (command === 'handleLogout') {
    ElMessageBox.confirm('退出后，需要重新登录才能使用。是否登出？', '确认登出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
      .then(async () => {
        await requesetLogout()
        appStore.logout()
      })
      .catch(() => {
        // Handle the cancellation action here
      })
  } else if (command === 'handleEditPwd') {
    editPasswordRef.value?.openDialog()
  }
}

const isFold = ref(false) // 侧边栏折叠状态
const handleChangeFold = (val: boolean) => {
  isFold.value = val
}
</script>

<template>
  <div>
    <!-- 隐藏收起过渡组件 -->
    <Transition name="fade">
      <div v-if="!isFold" class="sidebar">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <!-- 菜单名称 -->
          <span class="menus-name">iTerms 管理后台</span>
          <!-- 折叠按钮 -->
          <i class="iconfont icon-nexus-fold" @click="handleChangeFold(true)"></i>
        </div>
        <!-- 菜单列表 -->
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <el-menu :default-active="defaultActive" class="el-menu-vertical-demo" router>
            <template v-for="menu of menus" :key="menu.path">
              <!-- 有子菜单 -->
              <el-sub-menu v-if="menu.children && menu.children.length" :index="menu.path">
                <template #title>
                  <!-- 动态图标 -->
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
                <!-- 子菜单内容 -->
                <el-menu-item-group>
                  <el-menu-item v-for="childMenu of menu.children" :key="childMenu.path" :index="childMenu.path">
                    <i
                      :class="currentMenu == childMenu.path ? childMenu.iconActive : childMenu.icon"
                      class="iconfont"
                    ></i>
                    <span class="menu-title-sub">{{ childMenu.label }}</span>
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
              <!-- 没有子菜单 -->
              <el-menu-item v-else :key="menu.path" :index="menu.path" class="menu-wrap">
                <template #title>
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>
        <div class="footer">
          <div class="footer-icon">
            <SvgIcons name="user" width="2rem" height="2rem" />
          </div>
          <span class="footer-name">{{ appStore.userInfo?.realName }}</span>
          <el-dropdown placement="top-start" @command="handleConfig">
            <el-button type="primary">设置</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="handleLogout">退出登录</el-dropdown-item>
                <el-dropdown-item command="handleEditPwd">修改密码</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <span class="logout-icon">
            登出
            <i class="iconfont icon-nexus-arrowRight"></i>
          </span> -->
        </div>
      </div>
    </Transition>
    <!-- 折叠状态的展开按钮 -->
    <div v-show="isFold" class="sidebar-menus" @click="handleChangeFold(false)">
      <div class="sidebar-menus-icon">
        <i class="iconfont icon-nexus-unfold"></i>
      </div>
    </div>
    <EditPassword ref="editPasswordRef"></EditPassword>
  </div>
</template>

<style lang="scss" scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 220px;
  height: 100vh;
  padding: 16px 12px;
  white-space: nowrap;
  background-color: #f9f9fb;

  // 头部
  &-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 24px;
    margin-bottom: 16px;
    font-size: 16px;
    .menus-name {
      position: relative;
      flex: 1;
      margin-left: 12px;
      font-weight: 600;

      // &::before {
      //   position: absolute;
      //   top: 2px;
      //   left: -8px;
      //   width: 2px;
      //   height: 16px;
      //   content: '';
      //   background-color: #eaebf0;
      // }
    }
  }

  // 菜单列表
  .scrollbar-wrapper {
    flex: 1;
    width: 100%;
    .menu-title {
      margin-left: 8px;
      &-sub {
        margin-left: 4px;
      }
      .menu-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 40px;
        padding-left: 10px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: #773bef1a;
        }
      }
    }
  }
}

// 底部
.footer {
  z-index: 2;
  display: flex;
  align-items: center;
  width: 100%;
  height: 68px;
  padding-top: 16px;
  cursor: pointer;
  border-top: 1px solid var(--page-header-line);
  .footer-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
  }
  .footer-name {
    flex: 1;
    padding-left: 8px;
    color: var(--main-bg);
  }
}

// 图标样式
.iconfont {
  cursor: pointer;
}
.icon-nexus-unfold,
.icon-nexus-fold {
  font-size: 20px;
}

// 折叠按钮
.sidebar-menus {
  position: fixed;
  top: 8px;
  left: 0;
  width: 16px;
  height: 40px;
  transition: width 0.2s linear;
  &-icon {
    position: absolute;
    right: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    box-shadow: 0 4px 12px 0 rgb(34 29 57 / 10%);
  }
  &:hover {
    z-index: 5;
    width: 50px;
    color: var(--main-font);
  }
}

// 折叠动画效果
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s linear;
}
.fade-enter-from,
.fade-leave-to {
  width: 0;
  opacity: 0;
}

// 菜单列表组件
:deep(.el-scrollbar) {
  width: 100%;
  height: 100%;
}
:deep(.el-menu) {
  background-color: #f9f9fb;
  border-right: none;
}
:deep(.el-sub-menu__title) {
  height: 40px;
  padding: 0 0 0 8px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: #f9f9fb;
  &:hover {
    background: #773bef1a;
  }
}
:deep(.el-menu-item-group__title) {
  display: none;
}
:deep(.el-sub-menu__icon-arrow) {
  right: 6px;
  font-size: 14px;
}
:deep(.el-menu-item) {
  height: 40px;
  padding: 0 0 0 28px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: #f9f9fb;
  border-radius: 4px;
  &.is-active {
    color: #492ed1;
    background-color: #773bef1a;
  }
  &:hover {
    background: #773bef1a;
  }
}
:deep(.menu-wrap.el-menu-item) {
  padding-left: 10px !important;
}
</style>
