<script setup lang="ts">
const props = withDefaults(defineProps<{ height?: number; text: string; label: string }>(), {
  height: 31,
  text: '',
})

const isDragging = ref(false)
const refContent = ref()
const refInput = ref()

function onMouseDown() {
  isDragging.value = true
  document.body.style.cursor = 'row-resize'
  document.body.style.userSelect = 'none'
  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleMouseMove(e) {
  if (!isDragging.value) return
  const clientY = e.clientY
  const top = refContent.value.offsetTop
  let currentHeight = clientY - top
  if (currentHeight < props.height) currentHeight = props.height
  if (currentHeight > 800) currentHeight = 800
  refContent.value.style.height = currentHeight + 'px'
}

// 鼠标松开事件处理
function handleMouseUp() {
  isDragging.value = false
  document.body.style.cursor = ''
  document.body.style.userSelect = ''

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const emits = defineEmits(['update'])
const currentText = ref('')
const isEdit = ref(false)
function handleEdit() {
  isEdit.value = true
  currentText.value = props.text
}

function handleFocus() {
  isEdit.value && refInput.value.focus()
}
function handleCancel() {
  isEdit.value = false
}
function handleSave() {
  isEdit.value = false
  emits('update', currentText.value)
}
</script>
<template>
  <div class="item-wrap" ref="refContent">
    <span class="item-label">{{ label }}</span>
    <div class="item-content" @click="handleFocus" :class="{ 'content-edit': isEdit }">
      <div v-if="!isEdit" class="content">{{ text }}</div>
      <el-input ref="refInput" v-else v-model="currentText" type="textarea" autosize></el-input>
    </div>
    <div class="resize-bar" @mousedown="onMouseDown"></div>
    <el-button type="primary" link class="edit-btn" @click="handleEdit" v-if="!isEdit">编辑</el-button>
    <div class="save-btn" v-else>
      <el-button link @click="handleCancel">取消</el-button>
      <el-button type="primary" link @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.item-wrap {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 300px;
  margin-bottom: 12px;
  .item-label {
    display: inline-block;
    width: 80px;
    padding: 8px 12px 0 0;
    font-size: 14px;
    text-align: right;
  }
  .item-content {
    position: relative;
    flex: 1;
    width: 0;
    overflow-y: auto;
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
  }
  .content-edit {
    background-color: #fff;
  }
  .resize-bar {
    position: absolute;
    right: 0;
    bottom: -3px;
    width: calc(100% - 80px);
    height: 6px;
    cursor: row-resize;
    transition: background-color 0.3s;
    &:hover,
    &:active {
      background-color: #e8e8e8;
    }
  }
  .content {
    height: 100%;
    min-height: 31px;
    padding: 8px;
    line-height: 1.4;
    word-wrap: break-word;
  }
  .edit-btn {
    position: absolute;
    right: 12px;
    bottom: 4px;
    display: none;
  }
  .save-btn {
    position: absolute;
    right: 12px;
    bottom: 4px;
  }
  &:hover .edit-btn {
    display: block;
  }
}
:deep(.el-textarea__inner) {
  resize: none;
  background-color: #fff;
  border: 0;
  box-shadow: none;
}
</style>
