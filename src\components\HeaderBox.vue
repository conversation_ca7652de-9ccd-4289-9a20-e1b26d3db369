<script lang="ts" setup>
const router = useRouter()

defineProps({
  isShowBack: {
    type: Boolean,
    default: false, // 默认不显示
  },
})

const back = () => {
  router.go(-1)
}
</script>

<template>
  <div class="header-wrap">
    <div class="header-left">
      <i v-show="isShowBack" class="icon-nexus-back back" @click="back"></i>
      <div class="header-info">
        <div class="header-name">
          <slot name="header-title"></slot>
          <slot name="head-icon"></slot>
        </div>

        <div class="header-desc">
          <slot name="header-desc"></slot>
        </div>
      </div>
    </div>
    <slot name="option"></slot>
    <!-- 插槽示例代码（多个按钮需要放到一个容器中）
      <span>
        <el-button type="primary">操作按钮</el-button>
        <el-button>操作按钮</el-button>
      </span> 
    -->
  </div>
</template>

<style lang="scss" scoped>
.header-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 54px;
  padding: 0 16px;
  border-bottom: solid 1px #eaebf0;
  .header-left {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-info {
      display: flex;
      flex-direction: column;
      .header-name {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
      .header-desc {
        margin-top: 0.25rem;
        font-size: 12px;
        color: #999;
        letter-spacing: 0.5px;
      }
    }
  }
  .back {
    position: relative;
    padding: 5px;
    margin-right: 20px;
    cursor: pointer;
    border: 1px solid transparent;
    &::before {
      display: inline-block;
      font-size: 15px;
      font-weight: bold;
      color: #221d39;
    }
    &::after {
      position: absolute;
      top: 6px;
      right: -6px;
      width: 1px;
      height: 16px;
      content: '';
      background-color: #eaebf0;
    }
    &:hover {
      background-color: #f2f3f6;
      border-radius: 4px;
    }
  }
}
</style>
