import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'
const router: RouteRecordRaw[] = [
  // 审查清单
  {
    path: '/',
    component: Layout,
    meta: {
      title: '审查清单',
    },
    children: [
      {
        path: '/knowledge/checklist',
        name: 'Checklist',
        component: () => import('@/views/knowledge/ruleBase/list/index.vue'),
        // component: () => import(/* webpackChunkName: "checklist" */ '@/views/knowledge/ruleBase/list/index.vue'),
      },
    ],
  },
  // 审查清单
  {
    path: '/ruleDetail',
    name: 'RuleDetail',
    component: () => import(/* webpackChunkName: "checklist" */ '@/views/knowledge/ruleBase/detail/index.vue'),
    meta: { name: '规则库详情' },
  },
  // 法律法规
  {
    path: '/regulations',
    component: Layout,
    meta: {
      title: '法律法规',
    },
    children: [
      {
        path: '',
        name: 'Regulations',
        component: () => import(/* webpackChunkName: "regulations" */ '@/views/regulations/index.vue'),
      },
    ],
  },
  // 司法案例
  {
    path: '/judicialCase',
    component: Layout,
    meta: {
      title: '司法案例',
    },
    children: [
      {
        path: '',
        name: 'JudicialCase',
        component: () => import(/* webpackChunkName: "judicialCase" */ '@/views/judicialCase/index.vue'),
      },
    ],
  },
  // 合同范本
  {
    path: '/',
    component: Layout,
    meta: {
      title: '合同范本',
    },
    children: [
      {
        path: 'contract-template',
        name: 'ContractTemplate',
        component: () =>
          import(/* webpackChunkName: "contract-template" */ '@/views/knowledge/contract-template/index.vue'),
      },
    ],
  },
  // 合同类型管理
  {
    path: '/',
    component: Layout,
    meta: {
      title: '合同类型管理',
    },
    children: [
      {
        path: '/contractType',
        name: 'ContractType',
        component: () => import(/* webpackChunkName: "contractType" */ '@/views/contractType/index.vue'),
      },
    ],
  },
  // 标签管理
  {
    path: '/',
    component: Layout,
    meta: {
      title: '标签管理',
    },
    children: [
      {
        path: '/tags',
        name: 'Tags',
        component: () => import(/* webpackChunkName: "tags" */ '@/views/tags/index.vue'),
      },
    ],
  },
]
export default router
