<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TableLayout from '@/components/TableLayout/index.vue'
import type { TableColumn } from '@/components/TableLayout/types'
import SearchBox from './components/SearchBox.vue'
import CreateDialog from './components/CreateDialog.vue'
import { getContractTypeList, updateContractTypeStatus, type ContractTypeItem } from '@/services/contractApi'

// 表格引用
const tableLayoutRef = ref()

// 搜索参数
const searchParams = ref({})

// 表格数据
const tableData = ref<ContractTypeItem[]>([])

// 递归转换数据类型的函数
const convertStatusToBoolean = (item: any, parentDisabled = false, isTopLevel = true): any => {
  const isStatusEnabled = item.contractTypeStatus === 1 // 1 -> true, 0 -> false

  const convertedItem = {
    ...item,
    contractTypeStatus: isStatusEnabled,
    // 只有子类才设置disabled属性，父类（顶级）不设置
    disabled: isTopLevel ? false : parentDisabled || !isStatusEnabled,
  }

  // 如果有children，递归处理，传递当前项的禁用状态
  if (item.children && Array.isArray(item.children)) {
    convertedItem.children = item.children.map(
      (child: any) => convertStatusToBoolean(child, !isStatusEnabled, false), // 子级不是顶级
    )
  }

  return convertedItem
}

// 通用数据加载函数
const loadData = async (params: any = {}) => {
  try {
    const response = await getContractTypeList(params)
    console.log('API响应:', response)

    // 从API响应中提取数据
    const responseData = response.data as any
    let rawData = []
    if (responseData && responseData.records) {
      rawData = responseData.records
    } else {
      rawData = responseData || []
    }

    // 处理数据：递归将 contractTypeStatus 从数字转换为布尔值
    tableData.value = rawData.map(convertStatusToBoolean)

    console.log('表格数据:', tableData.value)
  } catch (error) {
    console.error('加载数据失败:', error)
    tableData.value = []
  }
}

// 加载合同类型数据
const loadContractTypeData = async () => {
  await loadData({ contractTypeName: '' })
}

// 表格列配置
const columns: TableColumn[] = [
  {
    prop: 'contractTypeName',
    label: '合同类型名称',
    minWidth: 200,
    slot: 'nameColumn',
  },
  {
    prop: 'contractTypeStatus',
    label: '状态',
    width: 100,
    slot: 'statusColumn',
  },
]

// 搜索参数处理
const handleSearchParams = async (params: Record<string, any>) => {
  searchParams.value = params
  console.log('搜索参数:', params)

  // 使用公共数据加载函数
  await loadData(params)
}

// 数据加载完成处理
const handleDataLoaded = (data: ContractTypeItem[]) => {
  console.log('合同类型数据加载完成:', data)
}

// 刷新表格
const refreshTable = () => {
  loadContractTypeData()
}

// 递归更新子类的disabled状态
const updateChildrenDisabled = (children: any[], parentDisabled: boolean) => {
  if (!children || !Array.isArray(children)) return

  children.forEach((child) => {
    child.disabled = parentDisabled
    // 如果子类还有子类，递归更新
    if (child.children && Array.isArray(child.children)) {
      updateChildrenDisabled(child.children, parentDisabled)
    }
  })
}

const beforeChange = (row: ContractTypeItem) => {
  return async () => {
    const newStatus = !row.contractTypeStatus // 即将变更的状态

    // 如果是要禁用，弹出确认弹窗
    if (!newStatus) {
      const text = row.parentId == '0' ? '子类型也将全部禁用' : ''
      try {
        await ElMessageBox.confirm('是否禁用该合同类型？' + text, '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch {
        // 用户取消，返回 false 阻止状态改变
        return false
      }
    }

    // 调用API更新状态（将布尔值转换为数字）
    try {
      await updateContractTypeStatus({
        id: row.id,
        contractTypeStatus: newStatus ? 1 : 0, // true -> 1, false -> 0
      })

      // 如果是父类且有子类，实时更新子类的disabled状态
      if (row.parentId === '0' && row.children && Array.isArray(row.children)) {
        updateChildrenDisabled(row.children, !newStatus) // 父类禁用时，子类disabled为true
      }

      ElMessage.success('操作成功')
      return true // 返回 true 允许状态改变
    } catch (error) {
      console.error('切换状态失败:', error)
      ElMessage.error('操作失败')
      return false // 返回 false 阻止状态改变
    }
  }
}

// 弹窗引用
const refCreate = ref<any>(null)

// 打开弹窗
const handleOpenDialog = (row?: any) => {
  if (row) {
    // 编辑模式 - 调用 CreateDialog 并传递编辑数据
    refCreate.value?.open(row)
  } else {
    // 新增模式 - 调用 CreateDialog
    refCreate.value?.open()
  }
}

// AI生成
const handleGenerative = () => {
  refCreate.value?.open()
}

// 添加子类型
const handleAddChild = (row: ContractTypeItem) => {
  // 传递父级信息给弹窗组件，用于创建子类型
  refCreate.value?.open(null, row)
}

// 页面挂载时加载数据
onMounted(() => {
  loadContractTypeData()
})
</script>
<template>
  <SearchBox @search="handleSearchParams" />
  <div style="padding: 16px; text-align: right">
    <el-button type="primary" @click="handleGenerative">新增合同类型</el-button>
  </div>
  <TableLayout
    ref="tableLayoutRef"
    :columns="columns"
    :tableData="tableData"
    @data-loaded="handleDataLoaded"
    :heightOffset="185"
    :showPagination="false"
  >
    <!-- 合同类型名称列插槽 -->
    <template #nameColumn="{ row }">
      <span class="name-text">
        {{ row.contractTypeName }}
      </span>
    </template>

    <!-- 状态列插槽 -->
    <template #statusColumn="{ row }">
      <el-switch
        v-model="row.contractTypeStatus"
        :before-change="beforeChange(row)"
        :disabled="row.parentId !== '0' && row.disabled"
      />
    </template>

    <!-- 操作列插槽 -->
    <template #actions="{ row }">
      <div class="action-buttons">
        <el-button type="text" @click="handleOpenDialog(row)">修改</el-button>
        <el-button v-if="row.parentId === '0'" type="text" @click="handleAddChild(row)"> 添加子类型 </el-button>
      </div>
    </template>
  </TableLayout>

  <!-- 弹窗组件 -->
  <CreateDialog ref="refCreate" @refresh="refreshTable" />
</template>
<style lang="scss" scoped>
.go-detail {
  color: var(--main-bg);
  cursor: pointer;
}
.status-dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-right: 5px;
  margin-bottom: 2px;
  border-radius: 50%;
}
.visibility-toggle {
  color: var(--main-bg);
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.warning-text {
  color: #e6555e !important;
}
</style>
