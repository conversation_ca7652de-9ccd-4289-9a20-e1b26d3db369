import { defineStore } from 'pinia'
import router from '@/router'
import type { IUser } from '@/services/user'

export const useAppStore = defineStore(
  'appStore',
  () => {
    const token = ref('')
    const userInfo = ref<IUser | null>(null)

    function setToken(tokenVal: string) {
      token.value = tokenVal
    }
    function setUserInfo(userInfoVal: IUser) {
      userInfo.value = userInfoVal
    }

    function logout() {
      token.value = ''
      userInfo.value = null
      router.push('/login')
    }

    return {
      token,
      userInfo,
      setToken,
      setUserInfo,
      logout,
    }
  },
  {
    persist: {
      key: 'appStore',
      storage: localStorage,
      pick: ['userInfo', 'token'], // Persist only userInfo and token
    },
  },
)
