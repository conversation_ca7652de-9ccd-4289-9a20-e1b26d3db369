/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    '(feat(表格组件)': 抽离表格组件)
    App: typeof import('./src/components/tinyflow-ai/demos/vue/src/App.vue')['default']
    AppMainBox: typeof import('./src/components/AppMainBox.vue')['default']
    AppMainTel: typeof import('./src/components/AppMainTel.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    HeaderBox: typeof import('./src/components/HeaderBox.vue')['default']
    LastUpdateBy: typeof import('./src/components/LastUpdateBy.vue')['default']
    OrgTree: typeof import('./src/components/orgTree/index.vue')['default']
    PageLayout: typeof import('./src/components/PageLayout.vue')['default']
    ProgressLoader: typeof import('./src/components/ProgressLoader.vue')['default']
    ResizeBar: typeof import('./src/components/ResizeBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcons: typeof import('./src/components/SvgIcons.vue')['default']
    TableHandle: typeof import('./src/components/TableHandle.vue')['default']
    TableLayout: typeof import('./src/components/TableLayout/index.vue')['default']
    Teleport: typeof import('./src/components/Teleport.vue')['default']
    TeleportApp: typeof import('./src/components/TeleportApp.vue')['default']
    TeleportMain: typeof import('./src/components/TeleportMain.vue')['default']
    Test: typeof import('./src/components/TableLayout/test.vue')['default']
    TextFold: typeof import('./src/components/TextFold.vue')['default']
    TextOvertip: typeof import('./src/components/TextOvertip.vue')['default']
    TextOverTip: typeof import('./src/components/TextOverTip.vue')['default']
    TextOverTooltip: typeof import('./src/components/TextOverTooltip.vue')['default']
    Tinyflow: typeof import('./src/components/Tinyflow.vue')['default']
    TinyflowWrapper: typeof import('./src/components/TinyflowWrapper.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
