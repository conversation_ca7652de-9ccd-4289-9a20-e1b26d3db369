<script setup lang="ts">
import SaveModelDialog from './SaveModelDialog.vue'
import type { IModelType } from '../api/index.ts'

defineProps<{
  modelTypeList: IModelType[]
}>()
const emits = defineEmits(['updateData'])

const saveModelDialogRef = ref<InstanceType<typeof SaveModelDialog>>()
const add = () => {
  saveModelDialogRef.value?.openDialog()
}

const updateData = () => {
  emits('updateData')
}
</script>

<template>
  <div>
    <div class="" align="right">
      <el-button type="primary" @click="add">新增模型</el-button>
    </div>
    <SaveModelDialog ref="saveModelDialogRef" @updateList="updateData" :model-type-list="modelTypeList" />
  </div>
</template>
