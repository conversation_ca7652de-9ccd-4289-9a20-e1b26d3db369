<script setup lang="ts">
import { ElLoading } from 'element-plus'
import Sidebar from './components/Sidebar.vue'
import Header from './components/Header.vue'
import { useAppStore } from '@/stores/app'
import { queryUserData } from '@/services/login'
import type { IUser } from '@/services/user'

const appStore = useAppStore()

const route = useRoute()
const isSingle = computed(() => route.meta.single)
const isCustom = computed(() => route.meta.custom)
async function initUserInfo() {
  const loadingInstance = ElLoading.service({ fullscreen: true })
  try {
    const { data } = await queryUserData()
    appStore.setUserInfo(data as IUser)
  } catch (err) {
    console.error(err)
  } finally {
    loadingInstance.close()
  }
}
initUserInfo()
</script>

<template>
  <div class="app-wrapper" :class="{ 'singe-wrapper': isSingle }">
    <Sidebar v-if="!isSingle" />
    <div class="app-main" id="app-main">
      <Header v-if="!isCustom" />
      <router-view class="app-conetent" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  height: 100%;
  .app-main {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;

    // overflow-x: auto;
  }
  .app-conetent {
    flex: 1;
    height: 0;
  }
}
.singe-wrapper {
  flex-direction: column;
}
</style>
