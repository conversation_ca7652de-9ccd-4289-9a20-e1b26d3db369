import { onMounted, onUnmounted, ref, type Ref } from 'vue'

export default function useWindowResize(): {
  width: Ref<number>
  height: Ref<number>
} {
  const width = ref<number>(window.innerWidth)
  const height = ref<number>(window.innerHeight)

  const handleResize = (): void => {
    width.value = window.innerWidth
    height.value = window.innerHeight
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return { width, height }
}
