{"name": "iterms-nexus-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "echo \"no build\"", "build:pnpm": "vue-tsc -b && vite build", "build:test": "vite build", "preview": "vite preview", "prepare": "husky install"}, "engines": {"node": ">=20.19.0"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iterms/tinyflow-ai-vue": "^0.2.3", "@tinyflow-ai/vue": "^1.0.4", "axios": "^1.10.0", "code-inspector-plugin": "^0.20.15", "element-plus": "^2.10.2", "jsencrypt": "^3.3.2", "knox-tool": "^2.0.27", "markdown-it": "14.1.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "postcss-pxtorem": "^6.1.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.17", "vue-json-viewer": "^2.2.22", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.0", "@microsoft/fetch-event-source": "^2.0.1", "@types/markdown-it": "^14.1.2", "@types/nprogress": "^0.2.3", "@types/postcss-pxtorem": "^6.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "code-inspector-plugin": "^0.20.15", "commitlint": "^19.8.1", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.2.0", "husky": "^9.1.7", "mockjs": "^1.1.0", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-pxtorem": "^6.1.0", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard-scss": "^15.0.1", "stylelint-order": "^7.0.0", "stylelint-prettier": "^5.0.3", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-static-copy": "^3.1.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.10"}}