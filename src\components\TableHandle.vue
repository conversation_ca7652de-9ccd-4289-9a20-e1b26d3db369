<script setup lang="ts">
const props = defineProps<{ options: { label: string; fn: any }[]; scopeRow: object }>()

const frontOptions = computed(() => props.options.slice(0, 2))
const moreOptions = computed(() => props.options.slice(2))
</script>

<template>
  <div class="box-wrap">
    <el-button-group class="el-button-right-line">
      <el-button type="primary" link @click="item.fn(scopeRow)" v-for="(item, idx) of frontOptions" :key="idx">{{
        item.label
      }}</el-button>
    </el-button-group>
    <el-dropdown v-if="moreOptions.length" placement="bottom">
      <el-button type="primary" link>更多</el-button>
      <template #dropdown>
        <el-dropdown-menu class="more-menu">
          <el-dropdown-item v-for="(item, idx) in moreOptions" :key="idx" class="more-item">
            <span @click="item.fn(scopeRow)">{{ item.label }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style lang="scss" scoped>
.box-wrap {
  display: flex;
}
</style>
