import request from '@/services'
import type { IResponse } from '@/services'

// 获取工作流列表参数
// 请求参数
export interface IWorkflowQueryData {
  wfName?: string
  wfDescription?: string
}
interface IQueryWorkflowListParams {
  pageNum: number
  pageSize: number
  data?: IWorkflowQueryData
}
// 响应参数
export interface Ilist {
  id: string
  wfName: string
  wfDescription: string
  appId: string
  wfGraph?: string
  createBy: string
  createByName: string
  createTime: string
  updateTime: string
  updateBy: string
  updateByName: string
}
export interface IQueryWorkflowList {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: Ilist[]
}

// 获取工作流列表接口
export function queryWorkflowList<T>(data: IQueryWorkflowListParams): Promise<IResponse<T>> {
  return request({
    url: `/workflow/queryPage`,
    method: 'post',
    data,
  })
}

// 删除工作流
// export function deleteWorkflowData<T>(id: string): Promise<IResponse<T>> {
//   return request({
//     url: `/model/remove/${id}`, //////// 路径要改
//     method: 'delete',
//   })
// }

// 保存工作流基础信息
export interface ISaveWorkflowInfoParams {
  wfName: string
  wfDescription: string
}

export function saveWorkflowInfo<T>(data: ISaveWorkflowInfoParams): Promise<IResponse<T>> {
  return request({
    url: `/workflow/saveBase`,
    method: 'post',
    data,
  })
}

export function updateWorkflowBaseInfo<T>(id: string, wfName: string, wfDescription: string): Promise<IResponse<T>> {
  return request({
    url: `/workflow/updateBaseInfo`,
    method: 'post',
    data: {
      id,
      wfName,
      wfDescription,
    },
  })
}
