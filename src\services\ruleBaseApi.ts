import request from '@/services'
import type { IResponse } from '@/services'

interface ISearchLastUpdatedByParams {
  realName: string
}

interface IUserInfo {
  userCode: string
  realName: string
}

// interface ILoginParams {
//   username: string
//   password: string
// }
// // 数据字典
// export function requesetLogin<T>(data: ILoginParams): Promise<IResponse<T>> {
//   return request({
//     url: `/auth/login`,
//     method: 'post',
//     data,
//   })
// }

export function queryStandpoint(data: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/list-rule-position`,
    method: 'post',
    data,
  })
}

// 获取合同类型列表 TODO
export function queryContractTypes(data: any): Promise<IResponse<any>> {
  return request({
    url: `/contract-type/list-all`,
    method: 'post',
    data,
  })
}

// 分页获取审查清单列表
export function queryRuleList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/search-page`,
    method: 'post',
    data,
  })
}

// 导出审查清单规则列表
export function downloadRuleList(data: any): Promise<any> {
  return request({
    url: `/llm/llm-review-rule-type/export-by-excel`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

export function deleteRuleList(params: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/remove-by-list-id`,
    method: 'delete',
    params,
  })
}

export function saveRuleList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/save`,
    method: 'post',
    data,
  })
}

export function queryDictionary(params: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/list-all-rule-by-rule-id`,
    method: 'get',
    params,
  })
}

export function createRuleList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/save`,
    method: 'post',
    data,
  })
}

export function delRuleId(params: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/remove-by-type-id`,
    method: 'delete',
    params,
  })
}

export function saveBatchRuleItem(data: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-item/save-batch`,
    method: 'post',
    data,
  })
}

export function queryRuleDetail(params: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-item/get-by-id`,
    method: 'get',
    params,
  })
}

export function uploadRuleFile(data: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/import-by-excel`,
    method: 'post',
    data,
  })
}

export function downloadRuleTemp(): Promise<any> {
  return request({
    url: `/rule_import_template.xlsx`,
    method: 'get',
    params: {},
    responseType: 'blob',
  })
}

export function uploadSortList(data: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/sort-list`,
    method: 'post',
    data,
  })
}

// AI生成审查清单
export function getAIGenerate(data: any): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/ai-generate`,
    method: 'post',
    data,
  })
}

// 获取审查清单解析状态
export function getAnalysisProgress(id: string | number): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/get-analysis-progress-by-rule-list-id`,
    method: 'get',
    params: { ruleListId: id },
  })
}

// 根据审查清单 id 获取对应的审查规则列表及子类
export function queryRuleTypeList(id: string | number): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/list-by-rule-list-id`,
    method: 'get',
    params: { ruleListId: id },
  })
}

// 根据审查清单 id 获取审查清单详情
export function getReviewDetails(id: string | number): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/get-by-id`,
    method: 'get',
    params: { id: id },
  })
}

// 查询所有审查清单
export function getAllRuleList(): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/get-all-rule-list`,
    method: 'get',
  })
}

// 根据审查列表id返回过滤规则内容
export function getFilterRuleTypeList(id: string | number): Promise<IResponse<any>> {
  return request({
    url: `/review-rule-list/filter/list-all-rule-by-rule-id`,
    method: 'get',
    params: { ruleListId: id },
  })
}

// 添加规则
export function saveBatchRuleItemCopy(data: any): Promise<IResponse<any>> {
  return request({
    url: `/llm/llm-review-rule-type/save-batch-type-and-item-from-copy`,
    method: 'post',
    data,
  })
}

// 搜索更新人
export function searchLastUpdatedBy(data: ISearchLastUpdatedByParams): Promise<IResponse<IUserInfo[]>> {
  return request({
    url: `/user/search-user-list-by-real-name`,
    method: 'post',
    data,
  })
}
// 合同上传-doc
export function postUpload(data: any): Promise<IResponse<any[]>> {
  return request({
    url: `/attachment/upload-temporary`,
    method: 'post',
    data,
  })
}
