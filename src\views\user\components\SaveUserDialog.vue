<script setup lang="ts">
import { type FormInstance } from 'element-plus'
import { addUserInfo, editUserInfo } from '@/services/user.ts'
import type { IAddUserInfoParams, IUser, IEditUserInfoParams } from '@/services/user.ts'

const emits = defineEmits(['updateList'])

const userID = ref('')
const title = computed(() => {
  return userID.value ? '编辑用户' : '新增用户'
})

const visible = ref(false)
const ruleFormRef = ref<FormInstance>()

const formData = ref<IAddUserInfoParams>({
  userName: '',
  userStatus: 1,
  realName: '',
})

const rules = {
  realName: [
    { required: true, message: '用户名称不能为空', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,20}$/,
      message: '用户名称长度需在2-20个字符之间，不能包含特殊符号',
      trigger: 'blur',
    },
  ],
  userName: [
    { required: true, message: '用户账号不能为空', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9]{2,15}$/,
      message: '账号格式不正确',
      trigger: 'blur',
    },
  ],
}

const openDialog = async (row?: IUser) => {
  visible.value = true
  if (row?.id) {
    userID.value = row.id
    formData.value = {
      userName: row.userName,
      userStatus: row.userStatus,
      realName: row.realName,
    }
  }
}

const closeDialog = () => {
  visible.value = false
  ruleFormRef.value?.resetFields()
}

// 新增 / 编辑
const saveUserData = async () => {
  await ruleFormRef.value?.validate()
  let params = {}
  if (userID.value) {
    params = {
      id: userID.value,
      ...formData.value,
    }
    try {
      const res = await editUserInfo(params as IEditUserInfoParams)
      if (res.code == '000000') {
        emits('updateList')
        ElMessage.success('编辑用户信息成功')
        closeDialog()
      }
    } catch (error) {
      ElMessage.error('编辑用户信息失败')
      console.log('验证失败', error)
    }
  } else {
    params = formData.value
    try {
      const res = await addUserInfo(params as IAddUserInfoParams)
      if (res.code == '000000') {
        emits('updateList')
        ElMessage.success('新增用户成功')
        closeDialog()
      }
    } catch (error) {
      ElMessage.error('新增用户失败')
      console.log('验证失败', error)
    }
  }
}

defineExpose({
  openDialog,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" width="600px" @close="closeDialog">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" class="right-form" label-position="top">
      <el-form-item label="用户名称" prop="realName">
        <el-input v-model="formData.realName" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="用户账号" prop="userName" v-if="!userID">
        <el-input v-model="formData.userName" placeholder="请输入用户账号" />
      </el-form-item>
      <el-form-item label="账号状态">
        <el-select v-model="formData.userStatus" placeholder="请选择账号状态">
          <el-option label="禁用" :value="0" />
          <el-option label="启用" :value="1" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="账号永久期" prop="type"></el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="saveUserData" :disabled="!formData.userName">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
