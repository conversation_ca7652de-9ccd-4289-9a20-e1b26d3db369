<script setup lang="ts">
import type { ISearchArgs } from '../types'
interface IProps {
  searchArgs: Partial<ISearchArgs>
}
const props = defineProps<IProps>()
const emits = defineEmits(['search', 'resetSearch', 'update:searchArgs'])

const searchData = computed({
  get: () => props.searchArgs,
  set: (val: ISearchArgs) => {
    emits('update:searchArgs', val)
  },
})

function handleReset() {
  emits('resetSearch')
}
function handleSearch() {
  emits('search')
}

const lawTypeOptions = [
  { value: 1, label: 'aaaa' },
  { value: 2, label: 'bbb' },
]
</script>

<template>
  <div class="search-wrap">
    <div class="condition">
      <span class="condition-label">文件名称</span>
      <el-input v-model="searchData.lawName" @keyup.enter="handleSearch" clearable placeholder="请输入" />
    </div>
    <div class="condition">
      <span class="condition-label">法律法规</span>
      <el-select v-model="searchData.lawType" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">时效性</span>
      <el-select v-model="searchData.timeliness" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">标签</span>
      <el-select v-model="searchData.tag" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">处理状态</span>
      <el-select v-model="searchData.status" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">可见范围</span>
      <el-select v-model="searchData.scope" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">数据来源</span>
      <el-select v-model="searchData.source" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in lawTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="condition">
      <span class="condition-label">创建人</span>
      <el-input v-model="searchData.creater" @keyup.enter="handleSearch" clearable placeholder="请输入" />
    </div>
    <div class="btn-wrap">
      <el-button type="primary" @click.stop="handleSearch">查 询</el-button>
      <el-button @click="handleReset">重 置</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px;
  padding: 16px 0;
  background-color: #fff;
  .condition {
    display: flex;
    align-items: center;
    width: 33.3%;
    &-label {
      width: 90px;
      padding-left: 12px;
      font-size: 14px;
      color: #606266;
    }
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100% !important;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .btn-wrap {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    padding-left: 12px;
  }
}
</style>
