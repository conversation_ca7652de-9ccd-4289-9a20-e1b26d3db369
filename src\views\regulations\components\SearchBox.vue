<script setup lang="ts">
import type { ILawSearchArgs } from '../types'
import { useGeneralService } from '@/composables/useGeneralService'

const { tagsData, queryTagsList, TagBizType } = useGeneralService()
const defaultArgs = {
  lawName: '',

  labelIds: [],
  shareScope: '',
}
const searchArgs = ref<ILawSearchArgs>({ ...defaultArgs })
const emits = defineEmits(['search'])

function handleReset() {
  searchArgs.value = { ...defaultArgs }
  emits('search', searchArgs.value)
}
function handleSearch() {
  emits('search', searchArgs.value)
}

const scopeOptions = [
  { value: 1, label: '后台可见' },
  { value: 2, label: '公开可见' },
]

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

onMounted(() => {
  queryTagsList(String(TagBizType.LAW))
})
</script>

<template>
  <div class="search-wrap">
    <div class="condition">
      <span class="condition-label">法律名称</span>
      <el-input v-model="searchArgs!.lawName" @keyup.enter="handleSearch" clearable placeholder="请输入" />
    </div>
    <div class="condition">
      <span class="condition-label">实效性</span>
      <el-select v-model="searchArgs!.currentValid" placeholder="请选择" clearable @change="handleSearch">
        <el-option :value="true" label="现行有效"></el-option>
        <el-option :value="false" label="已经失效"></el-option>
      </el-select>
    </div>

    <div class="condition">
      <span class="condition-label">可见范围</span>
      <el-select v-model="searchArgs!.shareScope" placeholder="请选择" clearable @change="handleSearch">
        <el-option v-for="item in scopeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>

    <div class="condition">
      <span class="condition-label">标签</span>

      <el-select v-model="searchArgs!.labelIds" multiple clearable @change="handleSearch">
        <el-option v-for="item in tagsData" :key="item.id" :label="item.labelName" :value="item.id"></el-option>
      </el-select>
    </div>

    <div class="btn-wrap">
      <el-button type="primary" @click.stop="handleSearch">查 询</el-button>
      <el-button @click="handleReset">重 置</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 1rem;
  padding: 1rem 0;
  background-color: #fff;
  .condition {
    display: flex;
    align-items: center;
    width: 20%;
    min-width: 18.75rem;
    &-label {
      width: 5.625rem;
      padding-left: 0.75rem;
      font-size: 0.875rem;
      color: #606266;
    }
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100% !important;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .btn-wrap {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    padding-left: 0.75rem;
  }
  .expand {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.25rem;
    margin-left: 0.625rem;
    color: var(--main-bg);
    cursor: pointer;
    background-color: var(--bg-color);
    border-radius: 50%;
  }
}
</style>
