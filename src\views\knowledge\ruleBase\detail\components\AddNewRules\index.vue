<template>
  <el-dialog v-model="visible" width="1500px" title="从审查库中选择" @closed="close">
    <div class="page-wrap-rules">
      <RulesList
        ref="refRulesList"
        :review-list="reviewList"
        :loading="loading"
        :review-rules="reviewRules"
        :loading-rules="loadingRules"
        @getReviewRuleData="getReviewRuleData"
        @getRuleDetails="getRuleDetails"
        @getSelectedNum="getSelectedNum"
      ></RulesList>
      <RuleDetails ref="refRuleDetails" class="rule-details">
        <template v-slot:header>
          <div class="details-header">规则详情</div>
        </template>
      </RuleDetails>
    </div>
    <template #footer>
      <span class="rules-page-option">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :disabled="!selectedNum" @click="submitRuleList">确认({{ selectedNum }})</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import RulesList from './components/RulesList.vue'
import RuleDetails from '../RightContent/components/RuleDetails.vue'
import { getAllRuleList, getFilterRuleTypeList, saveBatchRuleItemCopy } from '@/services/ruleBaseApi'

// 定义 emits 接口
interface Emits {
  addBatchRuleItem: []
}

// 定义组件实例类型
interface RulesListInstance {
  initData: () => void
  getCheckedRulesData: () => any[]
}

interface RuleDetailsInstance {
  renderRuleDetails: (node: any) => void
}

// 定义 emits
const emit = defineEmits<Emits>()

// 获取路由信息
const route = useRoute()
const ruleListId = route.query.id as string

// 组件引用
const refRulesList = ref<RulesListInstance | null>(null)
const refRuleDetails = ref<RuleDetailsInstance | null>(null)

// 响应式数据
const visible = ref(false)
const selectedNum = ref(0) // 已选择规则数量
const loading = ref(false)
const reviewList = ref([]) // 审查清单列表
const reviewRules = ref([]) // 审查规则列表
const loadingRules = ref(false)

// 显示/隐藏弹窗
const show = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
  refRulesList.value?.initData()
}

// 获取审查清单列表数据
const getReviewListData = async () => {
  if (loading.value) return
  loading.value = true

  try {
    const { data } = await getAllRuleList()
    reviewList.value = data
  } catch (error) {
    console.error('获取审查清单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取审查规则数据
const getReviewRuleData = async (id: string) => {
  selectedNum.value = 0 // 重置已选择规则数量
  loadingRules.value = true

  try {
    const { data } = await getFilterRuleTypeList(id)
    reviewRules.value = data.ruleTypeList
  } catch (error) {
    console.error('获取审查规则列表失败:', error)
  } finally {
    loadingRules.value = false
  }
}

// 获取规则详情
const getRuleDetails = (node: any) => {
  refRuleDetails.value?.renderRuleDetails(node)
}

// 提交规则列表
const submitRuleList = async () => {
  if (!refRulesList.value) return

  const rulesList = refRulesList.value.getCheckedRulesData()
  // 处理数据结构
  const processedList = rulesList.map((item) => {
    const ruleItem = {
      ...(item.ruleItem || {}),
      allRuleTypeName: item.allRuleTypeName,
      parentId: item.parentId,
    }
    return {
      ...item,
      ruleItem,
    }
  })
  const ruleItemsArray = processedList.map((item) => item.ruleItem)
  const params = {
    ruleListId: ruleListId,
    llmReviewRuleItemSaveBatchReqList: ruleItemsArray,
  }

  try {
    await saveBatchRuleItemCopy(params)
    ElMessage.success('保存成功')
    emit('addBatchRuleItem')
  } catch (error) {
    ElMessage.error('保存失败')
  }
  close()
}

// 获取已选择数量
const getSelectedNum = (num: number) => {
  selectedNum.value = num
}

// 暴露给父组件的方法
defineExpose({
  show,
})

// 组件挂载时获取数据
onMounted(async () => {
  await getReviewListData()
})
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: 0 !important;
  transform: translate(-50%, -50%);
  .el-dialog__body {
    padding: 0 10px;
  }
}
.page-wrap-rules {
  display: flex;
  align-items: center;
  height: 600px;
  .rule-details {
    padding: 0 0 20px 20px;
  }
  .details-header {
    padding-top: 10px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
  }
}
.rules-page-option {
  :deep(.el-button) {
    width: 100px;
  }
}
</style>
