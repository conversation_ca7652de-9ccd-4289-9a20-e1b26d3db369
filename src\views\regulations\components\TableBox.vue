<script setup lang="ts">
import EditDialog from './EditDialog.vue'

import emitter, { EmitEvents } from '@/utils/emitter'
import useWindowResize from '@/composables/useWindowResize'
import TableHandle from '@/components/TableHandle.vue'
import { useLawService } from './useLawService'

import type { ILawRecord, ILawSearchArgs } from '../types'
import router from '@/router'
const { height } = useWindowResize()
const {
  search,
  tableData,
  total,
  loading,
  page,
  pageSize,
  updateScope,
  handleDel,
  handleCurrentChange,
  handleSizeChange,
} = useLawService()
const refEditDialog = ref<InstanceType<typeof EditDialog>>()

const refViewScope = ref()

const currentLawId = ref<string>('')

const handleCheck = (id: string) => {
  currentLawId.value = id
  router.push(`/regulations/detail/${id}`)
}

const handleEdit = async (id: string) => {
  await refEditDialog.value?.open(id)
}
const selectedId = ref('')
const handleScope = (row: ILawRecord) => {
  selectedId.value = row.id

  refViewScope.value?.open(row.shareScope)
}
const setViewScope = (status: string) => {
  updateScope({ id: selectedId.value, shareScope: status })
}
const handleAgain = (id: string) => {
  console.log(id)
}

function setTablehandle(scopeRow: ILawRecord) {
  const handleOptions = [
    { label: '查看', fn: () => handleCheck(scopeRow.id) },
    { label: '编辑', fn: () => handleEdit(scopeRow.id) },
    { label: '删除', fn: () => handleDel(scopeRow.id) },
    { label: '可见范围', fn: () => handleScope(scopeRow) },
    // { label: '从新入库', fn: () => handleAgain(scopeRow.id) },
  ]
  return handleOptions
}

const maxHeight = ref(400)

onMounted(() => {
  search()
  maxHeight.value = height.value - 330

  emitter.on(EmitEvents.SEARCHBOX_EXPAND, (flag: any) => {
    if (!flag) {
      maxHeight.value = height.value - 240
    } else {
      maxHeight.value = height.value - 330
    }
  })
})
defineExpose({
  query(arg?: ILawSearchArgs) {
    search(arg)
  },
})
</script>

<template>
  <div class="table-wrap">
    <el-table
      ref="table"
      v-loading="loading"
      :max-height="maxHeight"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column label="法律名称" :show-overflow-tooltip="true" min-width="200">
        <template #default="scope">
          <span>{{ scope.row.lawName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="效率级别" :show-overflow-tooltip="true" min-width="200">
        <template #default="scope">
          <span>{{ scope.row.lawDegree || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时效性">
        <template #default="scope">
          <span>{{ scope.row.currentValid ? '现行有效' : '已经失效' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="可见范围">
        <template #default="scope">
          <span>{{ scope.row.shareScope === 100 ? '公开可见' : scope.row.shareScope === 0 ? '后台可见' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据来源">
        <template #default="scope">
          <span>{{ scope.row.originTypeName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <TableHandle :options="setTablehandle(scope.row)" :scope-row="scope.row" />
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="page"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>

    <EditDialog ref="refEditDialog" @close="search" />
    <ViewScope ref="refViewScope" @close="setViewScope" />"
  </div>
</template>

<style lang="scss" scoped>
.table-wrap {
  position: relative;
  flex: 1;
  padding-bottom: 42px;

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 12px;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }
  .top-handle {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding-right: 16px;
    .upload-btn {
      width: 80px;
    }
  }
}
</style>
