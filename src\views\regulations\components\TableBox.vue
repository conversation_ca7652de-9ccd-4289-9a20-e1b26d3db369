<script setup lang="ts">
import { queryLawRecord } from '@/services/regulations'
import EditDialog from './EditDialog.vue'
import ScopeDialog from './ScopeDialog.vue'
import LawTableBox from './LawTableBox/index.vue'
import TeleportApp from '@/components/TeleportApp.vue'
import TableHandle from '@/components/TableHandle.vue'
import type { ISearchArgs, ILawParams, ILawRecord, ILawResponse } from '../types'

interface IProps {
  searchArgs: Partial<ISearchArgs>
}
const props = defineProps<IProps>()

const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<ILawRecord[]>([])

const refEditDialog = ref<InstanceType<typeof EditDialog>>()
const refScopeDialog = ref<InstanceType<typeof ScopeDialog>>()

const getTableData = async () => {
  loading.value = true
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    data: {
      lawName: props.searchArgs.lawName,
    },
  }

  try {
    const res = await queryLawRecord<ILawResponse, ILawParams>(params)
    const { data } = res
    tableData.value = data.result
    total.value = data.count
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}
getTableData()

const reset = () => {
  page.value = 1
  getTableData()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  getTableData()
}

const handleCurrentChange = (val: number) => {
  page.value = val
  getTableData()
}

const handleDel = (id: string) => {
  ElMessageBox.confirm('确认删除这条记录吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    showClose: false,
  })
    .then(async () => {
      console.log(id)
      // await deleteSemanticsRecord(id)
      getTableData()
      ElMessage({
        type: 'success',
        message: '删除成功!',
      })
    })
    .catch(() => {
      console.log('取消删除比对记录')
    })
}

const currentLawId = ref<string>('')
const visibleDetail = ref(false)
const handleCheck = (id: string) => {
  console.log(id)
  currentLawId.value = id
  visibleDetail.value = true
}

const handleEdit = async (id: string) => {
  const flag = await refEditDialog.value?.open(id)
  console.log(flag)
}

const handleScope = (id: string) => {
  refScopeDialog.value?.open()
}

const handleAgain = (id: string) => {
  console.log(id)
}

function setTablehandle(scopeRow) {
  const handleOptions = [
    { label: '查看', fn: handleCheck },
    { label: '编辑', fn: handleEdit },
    { label: '删除', fn: handleDel },
    { label: '可见范围', fn: handleScope },
    { label: '从新入库', fn: handleAgain },
  ]
  return handleOptions
}

defineExpose({
  reset,
})
</script>

<template>
  <div class="table-wrap">
    <el-table
      ref="table"
      v-loading="loading"
      height="100%"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column label="法律名称" :show-overflow-tooltip="true" min-width="200">
        <template v-slot="scope">
          <span>{{ scope.row.lawName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="法律类型">
        <template v-slot="scope">
          <span>{{ scope.row.lawType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时效性">
        <template v-slot="scope">
          <span>{{ scope.row.timeliness }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态">
        <template v-slot="scope">
          <span>{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人">
        <template v-slot="scope">
          <span>{{ scope.row.creater }}</span>
        </template>
      </el-table-column>
      <el-table-column label="可见范围">
        <template v-slot="scope">
          <span>{{ scope.row.scope }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据来源">
        <template v-slot="scope">
          <span>{{ scope.row.source }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template v-slot="scope">
          <TableHandle :options="setTablehandle(scope.row)" :scope-row="scope.row" />
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="page"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
    <EditDialog ref="refEditDialog" />
    <ScopeDialog ref="refScopeDialog" />
  </div>
  <TeleportApp v-model:visible="visibleDetail" title="这是标题">
    <LawTableBox :currentLawId="currentLawId" />
  </TeleportApp>
</template>

<style lang="scss" scoped>
.table-wrap {
  position: relative;
  flex: 1;
  padding-bottom: 42px;

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 12px;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }
}
</style>
